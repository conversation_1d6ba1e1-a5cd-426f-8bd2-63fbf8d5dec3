const { Pool } = require('./backend/node_modules/pg');
const bcrypt = require('./backend/node_modules/bcryptjs');
const { v4: uuidv4 } = require('./backend/node_modules/uuid');

const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

async function createNewUsers() {
  try {
    console.log('🔐 Creating new users with complex passwords...');
    
    // Generate password hashes for the new passwords
    const adminHash = await bcrypt.hash('Admin2025!Secure#', 10);
    const userHash = await bcrypt.hash('User2025!Strong#', 10);
    const demoHash = await bcrypt.hash('Demo2025!Test#', 10);
    const testHash = await bcrypt.hash('Test2025!Complex#', 10);
    
    console.log('🧹 Deleting existing users...');
    // Delete existing users
    await pool.query('DELETE FROM users WHERE email LIKE \'%@test.com\' OR email LIKE \'%@gads-supercharge.today\' OR email LIKE \'%@gads.com\'');
    
    console.log('👥 Creating new users...');
    // Create new users with the exact passwords from tmp-logins.txt
    const adminId = uuidv4();
    const userId = uuidv4();
    const demoId = uuidv4();
    const testId = uuidv4();
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
      VALUES 
        ($1, '<EMAIL>', $2, 'admin', 'en', NOW(), NOW()),
        ($3, '<EMAIL>', $4, 'user', 'en', NOW(), NOW()),
        ($5, '<EMAIL>', $6, 'user', 'en', NOW(), NOW()),
        ($7, '<EMAIL>', $8, 'user', 'en', NOW(), NOW())
    `, [adminId, adminHash, userId, userHash, demoId, demoHash, testId, testHash]);
    
    console.log('✅ New users created successfully!');
    console.log('📧 <EMAIL> / Admin2025!Secure#');
    console.log('📧 <EMAIL> / User2025!Strong#');
    console.log('📧 <EMAIL> / Demo2025!Test#');
    console.log('📧 <EMAIL> / Test2025!Complex#');
    
    // Test login for admin
    console.log('🔐 Testing admin password...');
    const testResult = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (testResult.rows.length > 0) {
      const isValidPassword = await bcrypt.compare('Admin2025!Secure#', testResult.rows[0].password_hash);
      console.log('🔐 Admin password validation:', isValidPassword ? '✅ PASSED' : '❌ FAILED');
    }
    
    // Test login for user
    console.log('🔐 Testing user password...');
    const userTestResult = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (userTestResult.rows.length > 0) {
      const isValidUserPassword = await bcrypt.compare('User2025!Strong#', userTestResult.rows[0].password_hash);
      console.log('🔐 User password validation:', isValidUserPassword ? '✅ PASSED' : '❌ FAILED');
    }
    
    // Show all users
    console.log('\n📋 All users in database:');
    const allUsers = await pool.query('SELECT email, role, created_at FROM users ORDER BY created_at DESC');
    allUsers.rows.forEach(user => {
      console.log(`   ${user.email} (${user.role}) - ${user.created_at}`);
    });
    
    await pool.end();
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    await pool.end();
    process.exit(1);
  }
}

createNewUsers();
