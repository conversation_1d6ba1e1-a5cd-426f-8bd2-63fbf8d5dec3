const express = require('express');
const { query, param, validationResult } = require('express-validator');
const { query: dbQuery } = require('../database/connection');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * Get user's own activity log
 * GET /api/activity/my
 */
router.get('/my', [
  authenticateToken,
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
  query('type').optional().isString().withMessage('Type must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.userId;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const { type } = req.query;

    let queryText = `
      SELECT 
        id,
        activity_type,
        activity_data,
        ip_address,
        page_url,
        created_at
      FROM user_activities
      WHERE user_id = $1
    `;
    
    const queryParams = [userId];

    if (type) {
      queryText += ' AND activity_type = $2';
      queryParams.push(type);
    }

    queryText += ' ORDER BY created_at DESC LIMIT $' + (queryParams.length + 1) + ' OFFSET $' + (queryParams.length + 2);
    queryParams.push(limit, offset);

    const result = await dbQuery(queryText, queryParams);

    // Get total count
    let countQuery = 'SELECT COUNT(*) FROM user_activities WHERE user_id = $1';
    const countParams = [userId];
    
    if (type) {
      countQuery += ' AND activity_type = $2';
      countParams.push(type);
    }

    const countResult = await dbQuery(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    res.json({
      activities: result.rows,
      pagination: {
        limit,
        offset,
        total: totalCount,
        hasMore: offset + limit < totalCount
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching user activities:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch activities'
    });
  }
});

/**
 * Get activity statistics for user
 * GET /api/activity/stats
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get activity counts by type for last 30 days
    const statsResult = await dbQuery(`
      SELECT 
        activity_type,
        COUNT(*) as count,
        DATE(created_at) as date
      FROM user_activities
      WHERE user_id = $1 
        AND created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY activity_type, DATE(created_at)
      ORDER BY date DESC, activity_type
    `, [userId]);

    // Get total activity count
    const totalResult = await dbQuery(`
      SELECT COUNT(*) as total_activities
      FROM user_activities
      WHERE user_id = $1
    `, [userId]);

    // Get most recent activity
    const recentResult = await dbQuery(`
      SELECT activity_type, created_at
      FROM user_activities
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 1
    `, [userId]);

    // Get activity types summary
    const typesResult = await dbQuery(`
      SELECT 
        activity_type,
        COUNT(*) as count,
        MAX(created_at) as last_occurrence
      FROM user_activities
      WHERE user_id = $1
      GROUP BY activity_type
      ORDER BY count DESC
    `, [userId]);

    res.json({
      totalActivities: parseInt(totalResult.rows[0].total_activities),
      recentActivity: recentResult.rows[0] || null,
      activityTypes: typesResult.rows,
      dailyStats: statsResult.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching activity statistics:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch activity statistics'
    });
  }
});

/**
 * Get all users' activities (Admin only)
 * GET /api/activity/all
 */
router.get('/all', [
  authenticateToken,
  requireAdmin,
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
  query('userId').optional().isUUID().withMessage('User ID must be a valid UUID'),
  query('type').optional().isString().withMessage('Type must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const { userId, type } = req.query;

    let queryText = `
      SELECT 
        ua.id,
        ua.user_id,
        u.email,
        ua.activity_type,
        ua.activity_data,
        ua.ip_address,
        ua.page_url,
        ua.created_at
      FROM user_activities ua
      JOIN users u ON ua.user_id = u.id
      WHERE 1=1
    `;
    
    const queryParams = [];

    if (userId) {
      queryText += ' AND ua.user_id = $' + (queryParams.length + 1);
      queryParams.push(userId);
    }

    if (type) {
      queryText += ' AND ua.activity_type = $' + (queryParams.length + 1);
      queryParams.push(type);
    }

    queryText += ' ORDER BY ua.created_at DESC LIMIT $' + (queryParams.length + 1) + ' OFFSET $' + (queryParams.length + 2);
    queryParams.push(limit, offset);

    const result = await dbQuery(queryText, queryParams);

    // Get total count
    let countQuery = 'SELECT COUNT(*) FROM user_activities ua WHERE 1=1';
    const countParams = [];
    
    if (userId) {
      countQuery += ' AND ua.user_id = $' + (countParams.length + 1);
      countParams.push(userId);
    }

    if (type) {
      countQuery += ' AND ua.activity_type = $' + (countParams.length + 1);
      countParams.push(type);
    }

    const countResult = await dbQuery(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    res.json({
      activities: result.rows,
      pagination: {
        limit,
        offset,
        total: totalCount,
        hasMore: offset + limit < totalCount
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching all activities:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch activities'
    });
  }
});

/**
 * Get system-wide activity statistics (Admin only)
 * GET /api/activity/system-stats
 */
router.get('/system-stats', [authenticateToken, requireAdmin], async (req, res) => {
  try {
    // Get overall statistics
    const overallResult = await dbQuery(`
      SELECT 
        COUNT(*) as total_activities,
        COUNT(DISTINCT user_id) as active_users,
        COUNT(DISTINCT activity_type) as activity_types
      FROM user_activities
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `);

    // Get activity by type
    const typeResult = await dbQuery(`
      SELECT 
        activity_type,
        COUNT(*) as count,
        COUNT(DISTINCT user_id) as unique_users
      FROM user_activities
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY activity_type
      ORDER BY count DESC
    `);

    // Get daily activity for last 7 days
    const dailyResult = await dbQuery(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as activities,
        COUNT(DISTINCT user_id) as unique_users
      FROM user_activities
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);

    // Get most active users
    const usersResult = await dbQuery(`
      SELECT 
        u.email,
        COUNT(ua.id) as activity_count,
        MAX(ua.created_at) as last_activity
      FROM user_activities ua
      JOIN users u ON ua.user_id = u.id
      WHERE ua.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY u.id, u.email
      ORDER BY activity_count DESC
      LIMIT 10
    `);

    res.json({
      overall: overallResult.rows[0],
      activityByType: typeResult.rows,
      dailyActivity: dailyResult.rows,
      mostActiveUsers: usersResult.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching system statistics:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch system statistics'
    });
  }
});

module.exports = router;
