const express = require('express');
const { body, validationResult } = require('express-validator');
const { query: dbQuery } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');
const { logActivity, logLanguageChange } = require('../middleware/activityLogger');

const router = express.Router();

/**
 * Get user profile
 * GET /api/user/profile
 */
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const userResult = await dbQuery(`
      SELECT 
        id, 
        email, 
        role, 
        preferred_language, 
        created_at, 
        last_login_at
      FROM users 
      WHERE id = $1
    `, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    const user = userResult.rows[0];

    // Get user preferences
    const preferencesResult = await dbQuery(`
      SELECT preference_key, preference_value
      FROM user_preferences
      WHERE user_id = $1
    `, [userId]);

    const preferences = {};
    preferencesResult.rows.forEach(row => {
      preferences[row.preference_key] = row.preference_value;
    });

    res.json({
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        preferredLanguage: user.preferred_language,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      preferences,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch user profile'
    });
  }
});

/**
 * Update user language preference
 * PUT /api/user/language
 */
router.put('/language', [
  authenticateToken,
  body('language').isIn(['en', 'ua']).withMessage('Language must be en or ua')
], logLanguageChange, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.userId;
    const { language } = req.body;

    // Update user's preferred language
    await dbQuery(`
      UPDATE users 
      SET preferred_language = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [language, userId]);

    // Update or insert language preference
    await dbQuery(`
      INSERT INTO user_preferences (user_id, preference_key, preference_value)
      VALUES ($1, 'language', $2)
      ON CONFLICT (user_id, preference_key)
      DO UPDATE SET preference_value = $2, updated_at = CURRENT_TIMESTAMP
    `, [userId, JSON.stringify(language)]);

    res.json({
      message: 'Language preference updated successfully',
      language,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error updating language preference:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update language preference'
    });
  }
});

/**
 * Update user preferences
 * PUT /api/user/preferences
 */
router.put('/preferences', [
  authenticateToken,
  body('preferences').isObject().withMessage('Preferences must be an object')
], logActivity('preferences_update'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.userId;
    const { preferences } = req.body;

    // Update each preference
    for (const [key, value] of Object.entries(preferences)) {
      await dbQuery(`
        INSERT INTO user_preferences (user_id, preference_key, preference_value)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id, preference_key)
        DO UPDATE SET preference_value = $3, updated_at = CURRENT_TIMESTAMP
      `, [userId, key, JSON.stringify(value)]);
    }

    res.json({
      message: 'Preferences updated successfully',
      updatedKeys: Object.keys(preferences),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error updating preferences:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update preferences'
    });
  }
});

/**
 * Get user sessions
 * GET /api/user/sessions
 */
router.get('/sessions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const sessionsResult = await dbQuery(`
      SELECT 
        id,
        ip_address,
        browser_info,
        login_time,
        logout_time,
        is_active,
        expires_at
      FROM user_sessions
      WHERE user_id = $1
      ORDER BY login_time DESC
      LIMIT 10
    `, [userId]);

    const sessions = sessionsResult.rows.map(session => ({
      id: session.id,
      ipAddress: session.ip_address,
      browserInfo: session.browser_info,
      loginTime: session.login_time,
      logoutTime: session.logout_time,
      isActive: session.is_active,
      expiresAt: session.expires_at,
      isCurrent: session.id === req.user.sessionId
    }));

    res.json({
      sessions,
      count: sessions.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching user sessions:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch user sessions'
    });
  }
});

/**
 * Terminate a session
 * DELETE /api/user/sessions/:sessionId
 */
router.delete('/sessions/:sessionId', authenticateToken, logActivity('session_terminate'), async (req, res) => {
  try {
    const userId = req.user.userId;
    const { sessionId } = req.params;

    // Verify session belongs to user
    const sessionResult = await dbQuery(`
      SELECT id FROM user_sessions
      WHERE id = $1 AND user_id = $2
    `, [sessionId, userId]);

    if (sessionResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Session not found'
      });
    }

    // Terminate session
    await dbQuery(`
      UPDATE user_sessions
      SET is_active = false, logout_time = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [sessionId]);

    res.json({
      message: 'Session terminated successfully',
      sessionId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error terminating session:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to terminate session'
    });
  }
});

module.exports = router;
