const express = require('express');
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

// Apply authentication and admin check to all routes
router.use(authenticateToken);
router.use(requireAdmin);

// Tracking & Analytics Routes
router.get('/tracking-code', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT value FROM admin_settings WHERE key = $1',
      ['tracking_code']
    );
    
    res.json({ 
      code: result.rows.length > 0 ? result.rows[0].value : '' 
    });
  } catch (error) {
    console.error('Error fetching tracking code:', error);
    res.status(500).json({ error: 'Failed to fetch tracking code' });
  }
});

router.post('/tracking-code', async (req, res) => {
  try {
    const { code } = req.body;
    
    await pool.query(`
      INSERT INTO admin_settings (key, value, updated_at, updated_by) 
      VALUES ($1, $2, NOW(), $3)
      ON CONFLICT (key) DO UPDATE SET 
      value = EXCLUDED.value, 
      updated_at = NOW(), 
      updated_by = EXCLUDED.updated_by
    `, ['tracking_code', code, req.user.id]);

    // Log admin action
    await pool.query(`
      INSERT INTO user_activities (user_id, action, details, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [req.user.id, 'update_tracking_code', JSON.stringify({ code_length: code.length })]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving tracking code:', error);
    res.status(500).json({ error: 'Failed to save tracking code' });
  }
});

// SEO Management Routes
router.get('/seo/:page', async (req, res) => {
  try {
    const { page } = req.params;
    
    const result = await pool.query(
      'SELECT * FROM seo_settings WHERE page = $1',
      [page]
    );
    
    if (result.rows.length > 0) {
      res.json(result.rows[0]);
    } else {
      // Return default empty SEO data
      res.json({
        page,
        title_en: '',
        title_ua: '',
        description_en: '',
        description_ua: '',
        keywords_en: '',
        keywords_ua: '',
        verification_codes: ''
      });
    }
  } catch (error) {
    console.error('Error fetching SEO data:', error);
    res.status(500).json({ error: 'Failed to fetch SEO data' });
  }
});

router.post('/seo', async (req, res) => {
  try {
    const {
      page,
      title_en,
      title_ua,
      description_en,
      description_ua,
      keywords_en,
      keywords_ua,
      verification_codes
    } = req.body;

    await pool.query(`
      INSERT INTO seo_settings (
        page, title_en, title_ua, description_en, description_ua,
        keywords_en, keywords_ua, verification_codes, updated_at, updated_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9)
      ON CONFLICT (page) DO UPDATE SET
        title_en = EXCLUDED.title_en,
        title_ua = EXCLUDED.title_ua,
        description_en = EXCLUDED.description_en,
        description_ua = EXCLUDED.description_ua,
        keywords_en = EXCLUDED.keywords_en,
        keywords_ua = EXCLUDED.keywords_ua,
        verification_codes = EXCLUDED.verification_codes,
        updated_at = NOW(),
        updated_by = EXCLUDED.updated_by
    `, [page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua, verification_codes, req.user.id]);

    // Log admin action
    await pool.query(`
      INSERT INTO user_activities (user_id, action, details, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [req.user.id, 'update_seo_settings', JSON.stringify({ page })]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving SEO data:', error);
    res.status(500).json({ error: 'Failed to save SEO data' });
  }
});

// User Management Routes
router.get('/users', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT id, email, role, is_active, created_at, last_login_at, access_expiry_date
      FROM users 
      ORDER BY created_at DESC
    `);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

router.post('/users', async (req, res) => {
  try {
    const { email, password, role, access_expiry_date } = req.body;
    
    // Check if user already exists
    const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    const result = await pool.query(`
      INSERT INTO users (email, password_hash, role, access_expiry_date, created_at, updated_at)
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING id, email, role, is_active, created_at, access_expiry_date
    `, [email, hashedPassword, role, access_expiry_date || null]);

    // Log admin action
    await pool.query(`
      INSERT INTO user_activities (user_id, action, details, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [req.user.id, 'create_user', JSON.stringify({ created_user_email: email, role })]);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

router.put('/users/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;
    
    // Build dynamic update query
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    Object.keys(updates).forEach(key => {
      if (['email', 'role', 'is_active', 'access_expiry_date'].includes(key)) {
        updateFields.push(`${key} = $${paramCount}`);
        values.push(updates[key]);
        paramCount++;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updateFields.push(`updated_at = NOW()`);
    values.push(userId);

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, email, role, is_active, created_at, last_login_at, access_expiry_date
    `;

    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Log admin action
    await pool.query(`
      INSERT INTO user_activities (user_id, action, details, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [req.user.id, 'update_user', JSON.stringify({ updated_user_id: userId, updates })]);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Content Management Routes
router.get('/content-keys', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        ck.id,
        ck.key_name,
        ck.category,
        ck.description,
        COALESCE(
          json_object_agg(
            ct.language_code, ct.translation_text
          ) FILTER (WHERE ct.language_code IS NOT NULL),
          '{}'::json
        ) as translations
      FROM content_keys ck
      LEFT JOIN content_translations ct ON ck.id = ct.content_key_id
      GROUP BY ck.id, ck.key_name, ck.category, ck.description
      ORDER BY ck.category, ck.key_name
    `);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching content keys:', error);
    res.status(500).json({ error: 'Failed to fetch content keys' });
  }
});

router.put('/content/:keyId', async (req, res) => {
  try {
    const { keyId } = req.params;
    const { translations } = req.body;

    // Update translations for each language
    for (const [languageCode, translationText] of Object.entries(translations)) {
      await pool.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, updated_at)
        VALUES ($1, $2, $3, NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, languageCode, translationText]);
    }

    // Log admin action
    await pool.query(`
      INSERT INTO user_activities (user_id, action, details, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [req.user.id, 'update_content_translation', JSON.stringify({ content_key_id: keyId, languages: Object.keys(translations) })]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({ error: 'Failed to update content' });
  }
});

module.exports = router;
