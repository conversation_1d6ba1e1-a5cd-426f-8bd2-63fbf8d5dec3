const useragent = require('useragent');

/**
 * Parse user agent string to extract browser and OS information
 * @param {string} userAgentString - User agent string from request
 * @returns {Object} Parsed user agent information
 */
function parseUserAgent(userAgentString) {
  if (!userAgentString) {
    return {
      browser: 'Unknown',
      version: 'Unknown',
      os: 'Unknown',
      device: 'Unknown',
      isBot: false
    };
  }

  try {
    const agent = useragent.parse(userAgentString);
    
    // Detect if it's a bot
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i,
      /facebookexternalhit/i, /twitterbot/i, /linkedinbot/i
    ];
    
    const isBot = botPatterns.some(pattern => pattern.test(userAgentString));

    // Detect device type
    let deviceType = 'Desktop';
    if (/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgentString)) {
      if (/iPad/i.test(userAgentString)) {
        deviceType = 'Tablet';
      } else {
        deviceType = 'Mobile';
      }
    }

    return {
      browser: agent.family || 'Unknown',
      version: agent.toVersion() || 'Unknown',
      os: agent.os.toString() || 'Unknown',
      device: deviceType,
      isBot,
      raw: userAgentString
    };

  } catch (error) {
    console.error('Error parsing user agent:', error);
    return {
      browser: 'Unknown',
      version: 'Unknown',
      os: 'Unknown',
      device: 'Unknown',
      isBot: false,
      raw: userAgentString,
      error: error.message
    };
  }
}

/**
 * Get simplified browser name for analytics
 * @param {string} userAgentString - User agent string
 * @returns {string} Simplified browser name
 */
function getSimplifiedBrowser(userAgentString) {
  const parsed = parseUserAgent(userAgentString);
  const browser = parsed.browser.toLowerCase();

  if (browser.includes('chrome')) return 'Chrome';
  if (browser.includes('firefox')) return 'Firefox';
  if (browser.includes('safari')) return 'Safari';
  if (browser.includes('edge')) return 'Edge';
  if (browser.includes('opera')) return 'Opera';
  if (browser.includes('internet explorer') || browser.includes('ie')) return 'Internet Explorer';

  return parsed.browser;
}

/**
 * Check if user agent is from a mobile device
 * @param {string} userAgentString - User agent string
 * @returns {boolean} True if mobile device
 */
function isMobileDevice(userAgentString) {
  if (!userAgentString) return false;
  
  return /Mobile|Android|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgentString) &&
         !/iPad/i.test(userAgentString);
}

/**
 * Check if user agent is from a tablet device
 * @param {string} userAgentString - User agent string
 * @returns {boolean} True if tablet device
 */
function isTabletDevice(userAgentString) {
  if (!userAgentString) return false;
  
  return /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgentString);
}

/**
 * Check if user agent is from a bot/crawler
 * @param {string} userAgentString - User agent string
 * @returns {boolean} True if bot/crawler
 */
function isBot(userAgentString) {
  if (!userAgentString) return false;
  
  const botPatterns = [
    /bot/i, /crawler/i, /spider/i, /scraper/i,
    /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i,
    /facebookexternalhit/i, /twitterbot/i, /linkedinbot/i,
    /whatsapp/i, /telegram/i, /slack/i
  ];
  
  return botPatterns.some(pattern => pattern.test(userAgentString));
}

/**
 * Get device category for analytics
 * @param {string} userAgentString - User agent string
 * @returns {string} Device category (Mobile, Tablet, Desktop, Bot)
 */
function getDeviceCategory(userAgentString) {
  if (isBot(userAgentString)) return 'Bot';
  if (isMobileDevice(userAgentString)) return 'Mobile';
  if (isTabletDevice(userAgentString)) return 'Tablet';
  return 'Desktop';
}

module.exports = {
  parseUserAgent,
  getSimplifiedBrowser,
  isMobileDevice,
  isTabletDevice,
  isBot,
  getDeviceCategory
};
