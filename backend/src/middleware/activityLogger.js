const { query: dbQuery } = require('../database/connection');

/**
 * Middleware to log user activities
 * @param {string} activityType - Type of activity to log
 * @param {Object} options - Additional options
 */
function logActivity(activityType, options = {}) {
  return async (req, res, next) => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Log activity after response is sent
      setImmediate(async () => {
        try {
          if (req.user) {
            const activityData = {
              method: req.method,
              endpoint: req.originalUrl,
              statusCode: res.statusCode,
              userAgent: req.get('User-Agent'),
              ...options.additionalData,
              ...(options.includeBody && req.body ? { requestBody: req.body } : {}),
              ...(options.includeResponse && data ? { response: data } : {})
            };

            await dbQuery(`
              INSERT INTO user_activities (
                user_id, 
                session_id, 
                activity_type, 
                activity_data, 
                ip_address, 
                user_agent, 
                page_url
              )
              VALUES ($1, $2, $3, $4, $5, $6, $7)
            `, [
              req.user.userId,
              req.user.sessionId || null,
              activityType,
              JSON.stringify(activityData),
              req.ip || req.connection.remoteAddress,
              req.get('User-Agent'),
              req.originalUrl
            ]);
          }
        } catch (error) {
          console.error('Error logging activity:', error);
          // Don't fail the request if activity logging fails
        }
      });

      // Call original res.json
      return originalJson.call(this, data);
    };

    next();
  };
}

/**
 * Middleware to log page views
 */
function logPageView(req, res, next) {
  // Only log for authenticated users
  if (req.user) {
    setImmediate(async () => {
      try {
        await dbQuery(`
          INSERT INTO user_activities (
            user_id, 
            session_id, 
            activity_type, 
            activity_data, 
            ip_address, 
            user_agent, 
            page_url
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          req.user.userId,
          req.user.sessionId || null,
          'page_view',
          JSON.stringify({
            method: req.method,
            referrer: req.get('Referrer'),
            userAgent: req.get('User-Agent')
          }),
          req.ip || req.connection.remoteAddress,
          req.get('User-Agent'),
          req.originalUrl
        ]);
      } catch (error) {
        console.error('Error logging page view:', error);
      }
    });
  }

  next();
}

/**
 * Middleware to log API calls
 */
function logApiCall(req, res, next) {
  const startTime = Date.now();
  
  // Store original res.end to calculate response time
  const originalEnd = res.end;
  
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Log API call for authenticated users
    if (req.user) {
      setImmediate(async () => {
        try {
          await dbQuery(`
            INSERT INTO user_activities (
              user_id, 
              session_id, 
              activity_type, 
              activity_data, 
              ip_address, 
              user_agent, 
              page_url
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [
            req.user.userId,
            req.user.sessionId || null,
            'api_call',
            JSON.stringify({
              method: req.method,
              endpoint: req.originalUrl,
              statusCode: res.statusCode,
              responseTime: `${responseTime}ms`,
              userAgent: req.get('User-Agent'),
              contentLength: res.get('Content-Length')
            }),
            req.ip || req.connection.remoteAddress,
            req.get('User-Agent'),
            req.originalUrl
          ]);
        } catch (error) {
          console.error('Error logging API call:', error);
        }
      });
    }

    // Call original res.end
    return originalEnd.apply(this, args);
  };

  next();
}

/**
 * Middleware to log tool usage
 */
function logToolUsage(toolName, toolData = {}) {
  return logActivity('tool_usage', {
    additionalData: {
      toolName,
      ...toolData
    }
  });
}

/**
 * Middleware to log script generation
 */
function logScriptGeneration(scriptType, scriptData = {}) {
  return logActivity('script_generation', {
    additionalData: {
      scriptType,
      ...scriptData
    },
    includeBody: true
  });
}

/**
 * Middleware to log language changes
 */
function logLanguageChange(req, res, next) {
  if (req.user && req.body.language) {
    setImmediate(async () => {
      try {
        await dbQuery(`
          INSERT INTO user_activities (
            user_id, 
            session_id, 
            activity_type, 
            activity_data, 
            ip_address, 
            user_agent, 
            page_url
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          req.user.userId,
          req.user.sessionId || null,
          'language_change',
          JSON.stringify({
            newLanguage: req.body.language,
            previousLanguage: req.body.previousLanguage || null,
            userAgent: req.get('User-Agent')
          }),
          req.ip || req.connection.remoteAddress,
          req.get('User-Agent'),
          req.originalUrl
        ]);
      } catch (error) {
        console.error('Error logging language change:', error);
      }
    });
  }

  next();
}

module.exports = {
  logActivity,
  logPageView,
  logApiCall,
  logToolUsage,
  logScriptGeneration,
  logLanguageChange
};
