const jwt = require('jsonwebtoken');
const { query: dbQuery } = require('../database/connection');

/**
 * Middleware to authenticate JW<PERSON> token
 */
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if session is still active
    const sessionResult = await dbQuery(
      'SELECT id, user_id, is_active, expires_at FROM user_sessions WHERE session_token = $1',
      [token]
    );

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Session not found'
      });
    }

    const session = sessionResult.rows[0];

    // Check if session is active
    if (!session.is_active) {
      return res.status(401).json({
        error: 'Session expired',
        message: 'Session has been terminated'
      });
    }

    // Check if session has expired
    if (new Date() > new Date(session.expires_at)) {
      // Mark session as inactive
      await dbQuery(
        'UPDATE user_sessions SET is_active = false WHERE id = $1',
        [session.id]
      );

      return res.status(401).json({
        error: 'Session expired',
        message: 'Session has expired'
      });
    }

    // Verify user still exists and is active
    const userResult = await dbQuery(
      'SELECT id, email, role, is_active FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0 || !userResult.rows[0].is_active) {
      return res.status(401).json({
        error: 'Invalid user',
        message: 'User not found or inactive'
      });
    }

    // Add user info to request
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      role: decoded.role,
      sessionId: session.id
    };
    req.token = token;

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Token is malformed'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'Token has expired'
      });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Authentication failed'
    });
  }
}

/**
 * Middleware to check if user has admin role
 */
function requireAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'Please authenticate first'
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Admin access required'
    });
  }

  next();
}

/**
 * Middleware to check if user has specific role
 */
function requireRole(roles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please authenticate first'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: `Required role: ${roles.join(' or ')}`
      });
    }

    next();
  };
}

/**
 * Optional authentication middleware
 * Adds user info if token is present and valid, but doesn't require it
 */
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check session
    const sessionResult = await dbQuery(
      'SELECT id, user_id, is_active, expires_at FROM user_sessions WHERE session_token = $1',
      [token]
    );

    if (sessionResult.rows.length === 0 || !sessionResult.rows[0].is_active) {
      return next();
    }

    const session = sessionResult.rows[0];

    if (new Date() > new Date(session.expires_at)) {
      return next();
    }

    // Add user info if valid
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      role: decoded.role,
      sessionId: session.id
    };
    req.token = token;

    next();

  } catch (error) {
    // If there's an error with optional auth, just continue without user info
    next();
  }
}

module.exports = {
  authenticateToken,
  requireAdmin,
  requireRole,
  optionalAuth
};
