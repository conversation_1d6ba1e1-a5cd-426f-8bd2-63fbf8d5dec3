-- gAds Supercharge Database Schema
-- PostgreSQL initialization script

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    is_active BOOLEAN DEFAULT true,
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'ua')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Content management for multilingual support
CREATE TABLE content_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL, -- 'navigation', 'auth', 'tools', 'common', etc.
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Multilingual content storage
CREATE TABLE content_translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_key_id UUID NOT NULL REFERENCES content_keys(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'ua')),
    translation_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(content_key_id, language_code)
);

-- User sessions tracking
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    browser_info JSONB,
    login_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- User activity logging
CREATE TABLE user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    activity_type VARCHAR(100) NOT NULL, -- 'login', 'logout', 'page_view', 'tool_use', 'script_generate', etc.
    activity_data JSONB, -- Store additional activity-specific data
    ip_address INET,
    user_agent TEXT,
    page_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User preferences
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, preference_key)
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

CREATE INDEX idx_content_keys_name ON content_keys(key_name);
CREATE INDEX idx_content_keys_category ON content_keys(category);

CREATE INDEX idx_content_translations_key_lang ON content_translations(content_key_id, language_code);
CREATE INDEX idx_content_translations_language ON content_translations(language_code);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);

CREATE INDEX idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX idx_user_activities_session_id ON user_activities(session_id);
CREATE INDEX idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX idx_user_activities_created ON user_activities(created_at);

CREATE INDEX idx_user_preferences_user_key ON user_preferences(user_id, preference_key);

-- Update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_keys_updated_at BEFORE UPDATE ON content_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_translations_updated_at BEFORE UPDATE ON content_translations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (password: SuperAdmin2024!@#)
INSERT INTO users (email, password_hash, role) VALUES
('<EMAIL>', '$2a$12$8K.b2Z9FvFNmCeHIBQDOAOXEGvHyderQvJ8rUXvuLDHUwrKtQGzXK', 'admin');

-- Insert sample user (password: UserPass2024!@#)
INSERT INTO users (email, password_hash, role) VALUES
('<EMAIL>', '$2a$12$9L.c3A0GwGOnDfIJCREPBPYFHwIzefsSvK9sVYwvMEIVxsLuRHaYL', 'user');

-- Insert content keys and translations
INSERT INTO content_keys (key_name, category, description) VALUES
-- Navigation
('nav.dashboard', 'navigation', 'Dashboard navigation link'),
('nav.login', 'navigation', 'Login navigation link'),
('nav.logout', 'navigation', 'Logout navigation link'),
('nav.features', 'navigation', 'Features navigation link'),
('nav.portfolio', 'navigation', 'Portfolio navigation link'),

-- Common
('common.loading', 'common', 'Loading text'),
('common.error', 'common', 'Error text'),
('common.success', 'common', 'Success text'),
('common.cancel', 'common', 'Cancel button'),
('common.save', 'common', 'Save button'),
('common.delete', 'common', 'Delete button'),
('common.edit', 'common', 'Edit button'),
('common.back', 'common', 'Back button'),
('common.next', 'common', 'Next button'),
('common.previous', 'common', 'Previous button'),
('common.submit', 'common', 'Submit button'),
('common.generate', 'common', 'Generate button'),
('common.copy', 'common', 'Copy button'),
('common.download', 'common', 'Download button'),

-- Authentication
('auth.login', 'auth', 'Login text'),
('auth.email', 'auth', 'Email field label'),
('auth.password', 'auth', 'Password field label'),
('auth.loginButton', 'auth', 'Login button text'),
('auth.loginSuccess', 'auth', 'Login success message'),
('auth.loginError', 'auth', 'Login error message'),
('auth.logout', 'auth', 'Logout text'),
('auth.welcome', 'auth', 'Welcome message'),

-- Dashboard
('dashboard.title', 'dashboard', 'Dashboard page title'),
('dashboard.welcome', 'dashboard', 'Dashboard welcome message'),
('dashboard.description', 'dashboard', 'Dashboard description'),
('dashboard.tools', 'dashboard', 'Available tools section'),
('dashboard.recentActivity', 'dashboard', 'Recent activity section'),

-- Tools
('tools.telegram.title', 'tools', 'Telegram tool title'),
('tools.telegram.description', 'tools', 'Telegram tool description'),
('tools.airtable.title', 'tools', 'Airtable tool title'),
('tools.airtable.description', 'tools', 'Airtable tool description'),
('tools.budget.title', 'tools', 'Budget tool title'),
('tools.budget.description', 'tools', 'Budget tool description'),
('tools.campaign.title', 'tools', 'Campaign tool title'),
('tools.campaign.description', 'tools', 'Campaign tool description'),
('tools.keyword.title', 'tools', 'Keyword tool title'),
('tools.keyword.description', 'tools', 'Keyword tool description'),

-- Forms
('form.required', 'forms', 'Required field message'),
('form.invalid', 'forms', 'Invalid format message'),
('form.botToken', 'forms', 'Bot token field label'),
('form.chatId', 'forms', 'Chat ID field label'),
('form.accountTag', 'forms', 'Account tag field label'),
('form.apiKey', 'forms', 'API key field label'),
('form.url', 'forms', 'URL field label'),

-- Messages
('message.scriptGenerated', 'messages', 'Script generated success message'),
('message.scriptError', 'messages', 'Script generation error message'),
('message.copySuccess', 'messages', 'Copy success message'),
('message.copyError', 'messages', 'Copy error message'),

-- Footer
('footer.rights', 'footer', 'All rights reserved text'),
('footer.company', 'footer', 'Company name'),

-- Language
('language.english', 'language', 'English language name'),
('language.ukrainian', 'language', 'Ukrainian language name'),

-- Dashboard specific
('dashboard.welcomeBack', 'dashboard', 'Welcome back message with user name'),
('dashboard.quickActions', 'dashboard', 'Quick actions section'),
('dashboard.recentScripts', 'dashboard', 'Recent scripts section'),
('dashboard.analytics', 'dashboard', 'Analytics section'),

-- Tool specific content
('tools.telegram.botTokenLabel', 'tools', 'Bot token input label'),
('tools.telegram.chatIdLabel', 'tools', 'Chat ID input label'),
('tools.telegram.accountTagLabel', 'tools', 'Account tag input label'),
('tools.telegram.generateButton', 'tools', 'Generate script button'),
('tools.telegram.copyButton', 'tools', 'Copy script button'),
('tools.telegram.downloadButton', 'tools', 'Download script button'),
('tools.telegram.preview', 'tools', 'Script preview section'),
('tools.telegram.instructions', 'tools', 'Setup instructions'),

('tools.airtable.apiKeyLabel', 'tools', 'API key input label'),
('tools.airtable.baseUrlLabel', 'tools', 'Base URL input label'),
('tools.airtable.tableNameLabel', 'tools', 'Table name input label'),

-- Navigation specific
('nav.home', 'navigation', 'Home navigation link'),
('nav.tools', 'navigation', 'Tools navigation link'),
('nav.settings', 'navigation', 'Settings navigation link'),
('nav.help', 'navigation', 'Help navigation link'),

-- Buttons and actions
('button.save', 'buttons', 'Save button'),
('button.cancel', 'buttons', 'Cancel button'),
('button.delete', 'buttons', 'Delete button'),
('button.edit', 'buttons', 'Edit button'),
('button.create', 'buttons', 'Create button'),
('button.update', 'buttons', 'Update button'),
('button.close', 'buttons', 'Close button'),
('button.confirm', 'buttons', 'Confirm button'),

-- Status messages
('status.loading', 'status', 'Loading status'),
('status.saving', 'status', 'Saving status'),
('status.saved', 'status', 'Saved status'),
('status.error', 'status', 'Error status'),
('status.success', 'status', 'Success status'),
('status.warning', 'status', 'Warning status'),

-- Validation messages
('validation.required', 'validation', 'Required field validation'),
('validation.email', 'validation', 'Email validation'),
('validation.minLength', 'validation', 'Minimum length validation'),
('validation.maxLength', 'validation', 'Maximum length validation'),
('validation.invalidFormat', 'validation', 'Invalid format validation'),

-- Modal dialogs
('modal.confirmDelete', 'modals', 'Confirm delete modal title'),
('modal.confirmDeleteMessage', 'modals', 'Confirm delete modal message'),
('modal.unsavedChanges', 'modals', 'Unsaved changes modal title'),
('modal.unsavedChangesMessage', 'modals', 'Unsaved changes modal message'),

-- Tooltips and help
('tooltip.botToken', 'tooltips', 'Bot token tooltip'),
('tooltip.chatId', 'tooltips', 'Chat ID tooltip'),
('tooltip.apiKey', 'tooltips', 'API key tooltip'),
('tooltip.accountTag', 'tooltips', 'Account tag tooltip'),

-- Error messages
('error.networkError', 'errors', 'Network error message'),
('error.serverError', 'errors', 'Server error message'),
('error.unauthorized', 'errors', 'Unauthorized error message'),
('error.forbidden', 'errors', 'Forbidden error message'),
('error.notFound', 'errors', 'Not found error message'),
('error.validationFailed', 'errors', 'Validation failed error message'),

-- Success messages
('success.scriptGenerated', 'success', 'Script generated success message'),
('success.settingsSaved', 'success', 'Settings saved success message'),
('success.profileUpdated', 'success', 'Profile updated success message'),
('success.languageChanged', 'success', 'Language changed success message');

-- Insert English translations
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT id, 'en',
  CASE key_name
    -- Navigation
    WHEN 'nav.dashboard' THEN 'Dashboard'
    WHEN 'nav.login' THEN 'Login'
    WHEN 'nav.logout' THEN 'Logout'
    WHEN 'nav.features' THEN 'Features'
    WHEN 'nav.portfolio' THEN 'Portfolio'

    -- Common
    WHEN 'common.loading' THEN 'Loading...'
    WHEN 'common.error' THEN 'Error'
    WHEN 'common.success' THEN 'Success'
    WHEN 'common.cancel' THEN 'Cancel'
    WHEN 'common.save' THEN 'Save'
    WHEN 'common.delete' THEN 'Delete'
    WHEN 'common.edit' THEN 'Edit'
    WHEN 'common.back' THEN 'Back'
    WHEN 'common.next' THEN 'Next'
    WHEN 'common.previous' THEN 'Previous'
    WHEN 'common.submit' THEN 'Submit'
    WHEN 'common.generate' THEN 'Generate'
    WHEN 'common.copy' THEN 'Copy'
    WHEN 'common.download' THEN 'Download'

    -- Authentication
    WHEN 'auth.login' THEN 'Login'
    WHEN 'auth.email' THEN 'Email'
    WHEN 'auth.password' THEN 'Password'
    WHEN 'auth.loginButton' THEN 'Sign In'
    WHEN 'auth.loginSuccess' THEN 'Login successful!'
    WHEN 'auth.loginError' THEN 'Invalid credentials'
    WHEN 'auth.logout' THEN 'Logout'
    WHEN 'auth.welcome' THEN 'Welcome back!'

    -- Dashboard
    WHEN 'dashboard.title' THEN 'Dashboard'
    WHEN 'dashboard.welcome' THEN 'Welcome to gAds Supercharge'
    WHEN 'dashboard.description' THEN 'Your Google Ads automation and management platform'
    WHEN 'dashboard.tools' THEN 'Available Tools'
    WHEN 'dashboard.recentActivity' THEN 'Recent Activity'

    -- Tools
    WHEN 'tools.telegram.title' THEN 'Telegram Script Generator'
    WHEN 'tools.telegram.description' THEN 'Generate Google Ads scripts for Telegram notifications'
    WHEN 'tools.airtable.title' THEN 'Airtable Script Generator'
    WHEN 'tools.airtable.description' THEN 'Generate scripts for Airtable P&L reporting'
    WHEN 'tools.budget.title' THEN 'Budget Updater'
    WHEN 'tools.budget.description' THEN 'Automated budget management with notifications'
    WHEN 'tools.campaign.title' THEN 'Campaign Performance'
    WHEN 'tools.campaign.description' THEN 'Analyze campaign performance metrics'
    WHEN 'tools.keyword.title' THEN 'Keyword Performance'
    WHEN 'tools.keyword.description' THEN 'Track keyword performance and optimization'

    -- Forms
    WHEN 'form.required' THEN 'This field is required'
    WHEN 'form.invalid' THEN 'Invalid format'
    WHEN 'form.botToken' THEN 'Bot Token'
    WHEN 'form.chatId' THEN 'Chat ID'
    WHEN 'form.accountTag' THEN 'Account Tag'
    WHEN 'form.apiKey' THEN 'API Key'
    WHEN 'form.url' THEN 'URL'

    -- Messages
    WHEN 'message.scriptGenerated' THEN 'Script generated successfully!'
    WHEN 'message.scriptError' THEN 'Error generating script'
    WHEN 'message.copySuccess' THEN 'Copied to clipboard!'
    WHEN 'message.copyError' THEN 'Failed to copy'

    -- Footer
    WHEN 'footer.rights' THEN 'All rights reserved'
    WHEN 'footer.company' THEN 'gAds Supercharge'

    -- Language
    WHEN 'language.english' THEN 'English'
    WHEN 'language.ukrainian' THEN 'Українська'

    -- Dashboard specific
    WHEN 'dashboard.welcomeBack' THEN 'Welcome back!'
    WHEN 'dashboard.quickActions' THEN 'Quick Actions'
    WHEN 'dashboard.recentScripts' THEN 'Recent Scripts'
    WHEN 'dashboard.analytics' THEN 'Analytics'

    -- Tool specific content
    WHEN 'tools.telegram.botTokenLabel' THEN 'Bot Token'
    WHEN 'tools.telegram.chatIdLabel' THEN 'Chat ID'
    WHEN 'tools.telegram.accountTagLabel' THEN 'Account Tag'
    WHEN 'tools.telegram.generateButton' THEN 'Generate Script'
    WHEN 'tools.telegram.copyButton' THEN 'Copy Script'
    WHEN 'tools.telegram.downloadButton' THEN 'Download Script'
    WHEN 'tools.telegram.preview' THEN 'Script Preview'
    WHEN 'tools.telegram.instructions' THEN 'Setup Instructions'

    WHEN 'tools.airtable.apiKeyLabel' THEN 'API Key'
    WHEN 'tools.airtable.baseUrlLabel' THEN 'Base URL'
    WHEN 'tools.airtable.tableNameLabel' THEN 'Table Name'

    -- Navigation specific
    WHEN 'nav.home' THEN 'Home'
    WHEN 'nav.tools' THEN 'Tools'
    WHEN 'nav.settings' THEN 'Settings'
    WHEN 'nav.help' THEN 'Help'

    -- Buttons and actions
    WHEN 'button.save' THEN 'Save'
    WHEN 'button.cancel' THEN 'Cancel'
    WHEN 'button.delete' THEN 'Delete'
    WHEN 'button.edit' THEN 'Edit'
    WHEN 'button.create' THEN 'Create'
    WHEN 'button.update' THEN 'Update'
    WHEN 'button.close' THEN 'Close'
    WHEN 'button.confirm' THEN 'Confirm'

    -- Status messages
    WHEN 'status.loading' THEN 'Loading...'
    WHEN 'status.saving' THEN 'Saving...'
    WHEN 'status.saved' THEN 'Saved'
    WHEN 'status.error' THEN 'Error'
    WHEN 'status.success' THEN 'Success'
    WHEN 'status.warning' THEN 'Warning'

    -- Validation messages
    WHEN 'validation.required' THEN 'This field is required'
    WHEN 'validation.email' THEN 'Please enter a valid email address'
    WHEN 'validation.minLength' THEN 'Minimum length is {min} characters'
    WHEN 'validation.maxLength' THEN 'Maximum length is {max} characters'
    WHEN 'validation.invalidFormat' THEN 'Invalid format'

    -- Modal dialogs
    WHEN 'modal.confirmDelete' THEN 'Confirm Delete'
    WHEN 'modal.confirmDeleteMessage' THEN 'Are you sure you want to delete this item? This action cannot be undone.'
    WHEN 'modal.unsavedChanges' THEN 'Unsaved Changes'
    WHEN 'modal.unsavedChangesMessage' THEN 'You have unsaved changes. Do you want to save them before leaving?'

    -- Tooltips and help
    WHEN 'tooltip.botToken' THEN 'Your Telegram bot token from @BotFather'
    WHEN 'tooltip.chatId' THEN 'The chat ID where messages will be sent'
    WHEN 'tooltip.apiKey' THEN 'Your Airtable API key'
    WHEN 'tooltip.accountTag' THEN 'A unique identifier for your Google Ads account'

    -- Error messages
    WHEN 'error.networkError' THEN 'Network error. Please check your connection.'
    WHEN 'error.serverError' THEN 'Server error. Please try again later.'
    WHEN 'error.unauthorized' THEN 'You are not authorized to perform this action.'
    WHEN 'error.forbidden' THEN 'Access denied.'
    WHEN 'error.notFound' THEN 'The requested resource was not found.'
    WHEN 'error.validationFailed' THEN 'Validation failed. Please check your input.'

    -- Success messages
    WHEN 'success.scriptGenerated' THEN 'Script generated successfully!'
    WHEN 'success.settingsSaved' THEN 'Settings saved successfully!'
    WHEN 'success.profileUpdated' THEN 'Profile updated successfully!'
    WHEN 'success.languageChanged' THEN 'Language changed successfully!'
  END
FROM content_keys;

-- Insert Ukrainian translations
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT id, 'ua',
  CASE key_name
    -- Navigation
    WHEN 'nav.dashboard' THEN 'Панель керування'
    WHEN 'nav.login' THEN 'Вхід'
    WHEN 'nav.logout' THEN 'Вихід'
    WHEN 'nav.features' THEN 'Можливості'
    WHEN 'nav.portfolio' THEN 'Портфоліо'

    -- Common
    WHEN 'common.loading' THEN 'Завантаження...'
    WHEN 'common.error' THEN 'Помилка'
    WHEN 'common.success' THEN 'Успішно'
    WHEN 'common.cancel' THEN 'Скасувати'
    WHEN 'common.save' THEN 'Зберегти'
    WHEN 'common.delete' THEN 'Видалити'
    WHEN 'common.edit' THEN 'Редагувати'
    WHEN 'common.back' THEN 'Назад'
    WHEN 'common.next' THEN 'Далі'
    WHEN 'common.previous' THEN 'Попередній'
    WHEN 'common.submit' THEN 'Відправити'
    WHEN 'common.generate' THEN 'Генерувати'
    WHEN 'common.copy' THEN 'Копіювати'
    WHEN 'common.download' THEN 'Завантажити'

    -- Authentication
    WHEN 'auth.login' THEN 'Вхід'
    WHEN 'auth.email' THEN 'Електронна пошта'
    WHEN 'auth.password' THEN 'Пароль'
    WHEN 'auth.loginButton' THEN 'Увійти'
    WHEN 'auth.loginSuccess' THEN 'Успішний вхід!'
    WHEN 'auth.loginError' THEN 'Невірні дані для входу'
    WHEN 'auth.logout' THEN 'Вихід'
    WHEN 'auth.welcome' THEN 'Ласкаво просимо!'

    -- Dashboard
    WHEN 'dashboard.title' THEN 'Панель керування'
    WHEN 'dashboard.welcome' THEN 'Ласкаво просимо до gAds Supercharge'
    WHEN 'dashboard.description' THEN 'Ваша платформа для автоматизації та управління Google Ads'
    WHEN 'dashboard.tools' THEN 'Доступні інструменти'
    WHEN 'dashboard.recentActivity' THEN 'Остання активність'

    -- Tools
    WHEN 'tools.telegram.title' THEN 'Генератор Telegram скриптів'
    WHEN 'tools.telegram.description' THEN 'Генерація скриптів Google Ads для Telegram сповіщень'
    WHEN 'tools.airtable.title' THEN 'Генератор Airtable скриптів'
    WHEN 'tools.airtable.description' THEN 'Генерація скриптів для звітності P&L в Airtable'
    WHEN 'tools.budget.title' THEN 'Оновлювач бюджету'
    WHEN 'tools.budget.description' THEN 'Автоматизоване управління бюджетом з сповіщеннями'
    WHEN 'tools.campaign.title' THEN 'Ефективність кампаній'
    WHEN 'tools.campaign.description' THEN 'Аналіз метрик ефективності кампаній'
    WHEN 'tools.keyword.title' THEN 'Ефективність ключових слів'
    WHEN 'tools.keyword.description' THEN 'Відстеження та оптимізація ключових слів'

    -- Forms
    WHEN 'form.required' THEN 'Це поле обов''язкове'
    WHEN 'form.invalid' THEN 'Невірний формат'
    WHEN 'form.botToken' THEN 'Токен бота'
    WHEN 'form.chatId' THEN 'ID чату'
    WHEN 'form.accountTag' THEN 'Тег акаунту'
    WHEN 'form.apiKey' THEN 'API ключ'
    WHEN 'form.url' THEN 'URL'

    -- Messages
    WHEN 'message.scriptGenerated' THEN 'Скрипт успішно згенеровано!'
    WHEN 'message.scriptError' THEN 'Помилка генерації скрипту'
    WHEN 'message.copySuccess' THEN 'Скопійовано в буфер обміну!'
    WHEN 'message.copyError' THEN 'Не вдалося скопіювати'

    -- Footer
    WHEN 'footer.rights' THEN 'Всі права захищені'
    WHEN 'footer.company' THEN 'gAds Supercharge'

    -- Language
    WHEN 'language.english' THEN 'English'
    WHEN 'language.ukrainian' THEN 'Українська'

    -- Dashboard specific
    WHEN 'dashboard.welcomeBack' THEN 'Ласкаво просимо!'
    WHEN 'dashboard.quickActions' THEN 'Швидкі дії'
    WHEN 'dashboard.recentScripts' THEN 'Останні скрипти'
    WHEN 'dashboard.analytics' THEN 'Аналітика'

    -- Tool specific content
    WHEN 'tools.telegram.botTokenLabel' THEN 'Токен бота'
    WHEN 'tools.telegram.chatIdLabel' THEN 'ID чату'
    WHEN 'tools.telegram.accountTagLabel' THEN 'Тег акаунту'
    WHEN 'tools.telegram.generateButton' THEN 'Генерувати скрипт'
    WHEN 'tools.telegram.copyButton' THEN 'Копіювати скрипт'
    WHEN 'tools.telegram.downloadButton' THEN 'Завантажити скрипт'
    WHEN 'tools.telegram.preview' THEN 'Попередній перегляд скрипту'
    WHEN 'tools.telegram.instructions' THEN 'Інструкції з налаштування'

    WHEN 'tools.airtable.apiKeyLabel' THEN 'API ключ'
    WHEN 'tools.airtable.baseUrlLabel' THEN 'Base URL'
    WHEN 'tools.airtable.tableNameLabel' THEN 'Назва таблиці'

    -- Navigation specific
    WHEN 'nav.home' THEN 'Головна'
    WHEN 'nav.tools' THEN 'Інструменти'
    WHEN 'nav.settings' THEN 'Налаштування'
    WHEN 'nav.help' THEN 'Допомога'

    -- Buttons and actions
    WHEN 'button.save' THEN 'Зберегти'
    WHEN 'button.cancel' THEN 'Скасувати'
    WHEN 'button.delete' THEN 'Видалити'
    WHEN 'button.edit' THEN 'Редагувати'
    WHEN 'button.create' THEN 'Створити'
    WHEN 'button.update' THEN 'Оновити'
    WHEN 'button.close' THEN 'Закрити'
    WHEN 'button.confirm' THEN 'Підтвердити'

    -- Status messages
    WHEN 'status.loading' THEN 'Завантаження...'
    WHEN 'status.saving' THEN 'Збереження...'
    WHEN 'status.saved' THEN 'Збережено'
    WHEN 'status.error' THEN 'Помилка'
    WHEN 'status.success' THEN 'Успішно'
    WHEN 'status.warning' THEN 'Попередження'

    -- Validation messages
    WHEN 'validation.required' THEN 'Це поле обов''язкове'
    WHEN 'validation.email' THEN 'Будь ласка, введіть дійсну адресу електронної пошти'
    WHEN 'validation.minLength' THEN 'Мінімальна довжина {min} символів'
    WHEN 'validation.maxLength' THEN 'Максимальна довжина {max} символів'
    WHEN 'validation.invalidFormat' THEN 'Невірний формат'

    -- Modal dialogs
    WHEN 'modal.confirmDelete' THEN 'Підтвердити видалення'
    WHEN 'modal.confirmDeleteMessage' THEN 'Ви впевнені, що хочете видалити цей елемент? Цю дію неможливо скасувати.'
    WHEN 'modal.unsavedChanges' THEN 'Незбережені зміни'
    WHEN 'modal.unsavedChangesMessage' THEN 'У вас є незбережені зміни. Хочете зберегти їх перед виходом?'

    -- Tooltips and help
    WHEN 'tooltip.botToken' THEN 'Ваш токен Telegram бота від @BotFather'
    WHEN 'tooltip.chatId' THEN 'ID чату, куди будуть надсилатися повідомлення'
    WHEN 'tooltip.apiKey' THEN 'Ваш API ключ Airtable'
    WHEN 'tooltip.accountTag' THEN 'Унікальний ідентифікатор для вашого Google Ads акаунту'

    -- Error messages
    WHEN 'error.networkError' THEN 'Помилка мережі. Будь ласка, перевірте з''єднання.'
    WHEN 'error.serverError' THEN 'Помилка сервера. Будь ласка, спробуйте пізніше.'
    WHEN 'error.unauthorized' THEN 'У вас немає дозволу на виконання цієї дії.'
    WHEN 'error.forbidden' THEN 'Доступ заборонено.'
    WHEN 'error.notFound' THEN 'Запитуваний ресурс не знайдено.'
    WHEN 'error.validationFailed' THEN 'Помилка валідації. Будь ласка, перевірте введені дані.'

    -- Success messages
    WHEN 'success.scriptGenerated' THEN 'Скрипт успішно згенеровано!'
    WHEN 'success.settingsSaved' THEN 'Налаштування успішно збережено!'
    WHEN 'success.profileUpdated' THEN 'Профіль успішно оновлено!'
    WHEN 'success.languageChanged' THEN 'Мову успішно змінено!'
  END
FROM content_keys;
