{"name": "gads-backend", "version": "1.0.0", "description": "gAds Supercharge Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["google-ads", "api", "backend"], "author": "gAds Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "uuid": "^9.0.1", "useragent": "^2.3.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}