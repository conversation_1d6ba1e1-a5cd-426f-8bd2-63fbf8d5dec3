import React from 'react';
import { Helmet } from 'react-helmet-async';

type SEOProps = {
  title: string;
  description: string;
  path: string;
  type?: 'website' | 'article';
  image?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tag?: string;
};

const SEOComponent: React.FC<SEOProps> = ({
  title,
  description,
  path,
  type = 'website',
  image = '/og-image.jpg',
  publishedTime,
  modifiedTime,
  author = 'gAds Team',
  section,
  tag,
}) => {
  const siteUrl = 'https://gads.example.com';
  const fullUrl = `${siteUrl}${path}`;
  const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;

  return (
    <Helmet>
      {/* Standard metadata */}
      <title>{title} | gAds Services</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:site_name" content="gAds Services" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={fullUrl} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:creator" content="@gads_services" />

      {/* Additional meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="theme-color" content="#1e40af" />

      {/* Schema.org markup for Google */}
      <script type="application/ld+json">
        {JSON.stringify({
          '@context': 'https://schema.org',
          '@type': type === 'article' ? 'Article' : 'WebPage',
          name: title,
          description: description,
          url: fullUrl,
          publisher: {
            '@type': 'Organization',
            name: 'gAds',
            logo: {
              '@type': 'ImageObject',
              url: `${siteUrl}/logo-192x192.png`,
            },
          },
          image: fullImageUrl,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': fullUrl,
          },
          ...(type === 'article' && {
            author: {
              '@type': 'Person',
              name: author,
            },
            datePublished: publishedTime,
            dateModified: modifiedTime || publishedTime,
            articleSection: section,
            articleTag: tag,
            publisher: {
              '@type': 'Organization',
              name: 'gAds',
              logo: {
                '@type': 'ImageObject',
                url: `${siteUrl}/logo-192x192.png`,
              },
            },
          }),
        })}
      </script>
    </Helmet>
  );
};

export default SEOComponent;
