import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const CallToAction: React.FC = () => {
  const { t } = useLanguage();

  const features = [
    t('homepage.cta.feature1'),
    t('homepage.cta.feature2'),
    t('homepage.cta.feature3'),
    t('homepage.cta.feature4')
  ];

  return (
    <section className="py-28 bg-gradient-to-br from-blue-900/40 to-cyan-900/40 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/[0.03] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-white">{t('homepage.cta.title')}</h2>
          <p className="text-xl text-blue-100/80 mb-12 text-center max-w-3xl mx-auto">
            {t('homepage.cta.subtitle')}
          </p>
          <div className="max-w-4xl mx-auto mb-16">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((item, index) => (
                <div key={index} className="flex items-start bg-white/5 backdrop-blur-sm p-5 rounded-xl border border-white/10 hover:border-blue-400/30 transition-colors">
                  <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-600/20 text-blue-400 mr-4 flex-shrink-0">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </span>
                  <span className="text-lg text-blue-100/90">{item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;