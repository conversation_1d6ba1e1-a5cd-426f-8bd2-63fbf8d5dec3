import React from 'react';
import { Link } from 'react-router-dom';
import { services } from '../data/services';
import { ArrowRight, LayoutDashboard, BarChart, Rocket, Search } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const iconMap = {
  'layout-dashboard': LayoutDashboard,
  'bar-chart': <PERSON><PERSON><PERSON>,
  'rocket': Rocket,
  'search': Search,
};

const Services: React.FC = () => {
  const { t } = useLanguage();

  return (
    <section className="py-28 bg-gradient-to-b from-gray-900 to-gray-800 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/[0.03] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('homepage.services.title')}</h2>
          <p className="text-lg text-blue-100/80 max-w-2xl mx-auto">
            {t('homepage.services.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            {
              id: 1,
              title: t('homepage.services.campaign.title'),
              description: t('homepage.services.campaign.description'),
              icon: 'layout-dashboard',
            },
            {
              id: 2,
              title: t('homepage.services.analytics.title'),
              description: t('homepage.services.analytics.description'),
              icon: 'bar-chart',
            },
            {
              id: 3,
              title: t('homepage.services.growth.title'),
              description: t('homepage.services.growth.description'),
              icon: 'rocket',
            },
            {
              id: 4,
              title: t('homepage.services.targeting.title'),
              description: t('homepage.services.targeting.description'),
              icon: 'search',
            },
          ].map((service) => {
            const Icon = iconMap[service.icon as keyof typeof iconMap];

            return (
              <div key={service.id} className="bg-white/5 backdrop-blur-sm p-8 rounded-xl border border-white/10 hover:border-blue-400/30 transition-all duration-300 hover:shadow-lg flex flex-col h-full">
                <div className="mb-4 p-3 bg-blue-600/20 rounded-lg inline-flex">
                  <Icon className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{service.title}</h3>
                <p className="text-blue-100/70 mb-6 flex-grow">{service.description}</p>
                <Link
                  to="/contact"
                  className="inline-flex items-center text-blue-400 font-medium group hover:text-blue-300 transition-colors mt-auto"
                >
                  {t('homepage.services.get_started')}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Services;