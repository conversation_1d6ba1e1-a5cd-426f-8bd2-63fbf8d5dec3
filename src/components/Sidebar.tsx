import React, { useEffect, useRef, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Table, Send, Settings, Home, Zap, BarChart2, DollarSign, Search, Maximize2, FileText, Smartphone, GitCompare, ChevronDown, ChevronUp } from 'lucide-react';
import { useSidebar } from '../contexts/SidebarContext';
import { useLanguage } from '../contexts/LanguageContext';

interface NavItemType {
  name: string;
  icon: React.ReactNode;
  path: string;
}

const NavItem: React.FC<{ 
  item: NavItemType; 
  isActive: boolean; 
  isChild?: boolean;
}> = ({ 
  item, 
  isActive, 
  isChild = false
}) => {
  return (
    <Link
      to={item.path}
      className={`flex items-center space-x-3 px-4 py-2.5 rounded-lg transition-colors duration-200 ${
        isActive
          ? 'bg-blue-900/50 text-white'
          : 'text-gray-400 hover:bg-gray-800/50 hover:text-white'
      } ${isChild ? 'pl-8 text-sm' : ''}`}
      title={item.name}
    >
      <span className="flex-shrink-0">{item.icon}</span>
      <span className="text-sm font-medium">{item.name}</span>
    </Link>
  );
};

interface DropdownItem extends NavItemType {
  items: NavItemType[];
}

const Sidebar: React.FC = () => {
  const location = useLocation();
  const { isCollapsed, toggleSidebar } = useSidebar();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const { t } = useLanguage();
  
  const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);

  const toggleDropdownInternal = (name: string) => {
    setOpenDropdown(openDropdown === name ? null : name);
  };

  const mainNavItems: NavItemType[] = [
    {
      name: t('sidebar.dashboard') || 'Dashboard',
      icon: <Home className="w-5 h-5" />,
      path: '/dashboard'
    },
    {
      name: t('sidebar.airtable') || 'Airtable P&L',
      icon: <Table className="w-5 h-5" />,
      path: '/dashboard/airtable-script'
    },
    {
      name: t('sidebar.telegram') || 'Telegram Script',
      icon: <Send className="w-5 h-5" />,
      path: '/dashboard/telegram-script-generator'
    },
    {
      name: t('sidebar.budget_updater') || 'Budget Updater',
      icon: <DollarSign className="w-5 h-5" />,
      path: '/dashboard/gads-budget-updater'
    },
    {
      name: t('sidebar.budget_monitor') || 'Budget Monitor',
      icon: <DollarSign className="w-5 h-5" />,
      path: '/dashboard/budget-monitor'
    },
    {
      name: t('sidebar.performance_max') || 'Performance Max',
      icon: <Maximize2 className="w-5 h-5" />,
      path: '/dashboard/performance-max'
    },
    {
      name: t('sidebar.device_bid') || 'Device Bid Adjuster',
      icon: <Smartphone className="w-5 h-5" />,
      path: '/dashboard/device-bid'
    }
  ];

  const dropdownItems: DropdownItem[] = [
    {
      name: t('sidebar.campaign_tools') || 'Campaign Tools',
      icon: <BarChart2 className="w-5 h-5" />,
      path: '#',
      items: [
        {
          name: t('sidebar.campaign_performance') || 'Campaign Performance',
          icon: <BarChart2 className="w-5 h-5" />,
          path: '/dashboard/campaign-performance'
        },
        {
          name: t('sidebar.ad_performance') || 'Ad Performance',
          icon: <BarChart2 className="w-5 h-5" />,
          path: '/dashboard/ad-performance'
        },
        {
          name: t('sidebar.keyword_performance') || 'Keyword Performance',
          icon: <BarChart2 className="w-5 h-5" />,
          path: '/dashboard/keyword-performance'
        },
        {
          name: t('sidebar.search_query') || 'Search Query Performance',
          icon: <Search className="w-5 h-5" />,
          path: '/dashboard/search-query'
        }
      ]
    },
    {
      name: t('sidebar.search_tools') || 'Search Tools',
      icon: <Settings className="w-5 h-5" />,
      path: '#',
      items: [
        {
          name: t('sidebar.script_generator') || 'Script Generator',
          icon: <FileText className="w-5 h-5" />,
          path: '/dashboard/script-generator'
        },
        {
          name: t('sidebar.keyword_conflict') || 'Keyword Conflict Detector',
          icon: <GitCompare className="w-5 h-5" />,
          path: '/dashboard/keyword-conflict'
        }
      ]
    }
  ];

  // Function to check if current path belongs to a dropdown
  const isDropdownActive = useCallback((dropdown: DropdownItem): boolean => {
    return dropdown.items.some(item => location.pathname === item.path);
  }, [location.pathname]);

  // Function to check if dropdown should be open (either manually opened or auto-opened due to active path)
  const isDropdownOpen = useCallback((dropdown: DropdownItem): boolean => {
    return openDropdown === dropdown.name || isDropdownActive(dropdown);
  }, [openDropdown, isDropdownActive]);

  // Auto-open dropdown if current path is one of its children
  useEffect(() => {
    const activeDropdown = dropdownItems.find(dropdown => isDropdownActive(dropdown));
    if (activeDropdown && openDropdown !== activeDropdown.name) {
      setOpenDropdown(activeDropdown.name);
    }
  }, [location.pathname, isDropdownActive, openDropdown]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div 
      ref={sidebarRef}
      className={`hidden md:block fixed md:relative h-full bg-gray-900/60 text-white transition-all duration-300 ease-in-out z-30 w-64 hover:bg-gray-900/80`}
    >
      <div className="px-4 pt-8 pb-3">
        <div className="flex items-center space-x-3 group cursor-pointer mb-3" onClick={isCollapsed ? toggleSidebar : undefined}>
          <div className="p-1.5 rounded-lg bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors duration-200">
            <Zap className="w-5 h-5 text-blue-400" />
          </div>
          {!isCollapsed && (
            <span className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
              {t('sidebar.tools') || 'Tools'}
            </span>
          )}
        </div>

        {!isCollapsed && (
          <nav className="space-y-1 flex-grow overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-800 pr-1">
            {mainNavItems.map((item) => (
              <NavItem key={item.name} item={item} isActive={location.pathname === item.path} />
            ))}
            {dropdownItems.map((dropdown) => (
              <div key={dropdown.name}>
                <button
                  onClick={() => toggleDropdownInternal(dropdown.name)}
                  className="w-full flex items-center justify-between space-x-3 px-4 py-2.5 rounded-lg transition-colors duration-200 text-gray-400 hover:bg-gray-800/50 hover:text-white"
                  title={dropdown.name}
                >
                  <div className="flex items-center space-x-3">
                    <span className="flex-shrink-0">{dropdown.icon}</span>
                    <span className="text-sm font-medium">{dropdown.name}</span>
                  </div>
                  {isDropdownOpen(dropdown) ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                </button>
                {isDropdownOpen(dropdown) && (
                  <div className="mt-1 space-y-1">
                    {dropdown.items.map((item) => (
                      <NavItem key={item.name} item={item} isActive={location.pathname === item.path} isChild />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
        )}
      </div>

      {!isCollapsed && (
        <div className="px-4 pb-4 pt-2 mt-auto border-t border-gray-700/50">
          <Link
            to="/dashboard/settings"
            className={`flex items-center space-x-3 px-4 py-2.5 rounded-lg transition-colors duration-200 ${
              location.pathname === '/dashboard/settings'
                ? 'bg-blue-900/50 text-white'
                : 'text-gray-400 hover:bg-gray-800/50 hover:text-white'
            }`}
            title="Settings"
          >
            <Settings className="w-5 h-5" />
            <span className="text-sm font-medium">{t('sidebar.settings') || 'Settings'}</span>
          </Link>
        </div>
      )}
    </div>
  );
};

const CollapsedSidebar: React.FC = () => {
  const location = useLocation();
  const { toggleSidebar } = useSidebar();
  const { t } = useLanguage();

  const mainNavItemsCollapsed = [
    { name: t('sidebar.dashboard') || 'Dashboard', icon: <Home className="w-6 h-6" />, path: '/dashboard' },
    { name: t('sidebar.airtable') || 'Airtable P&L', icon: <Table className="w-6 h-6" />, path: '/dashboard/airtable-script' },
    { name: t('sidebar.telegram') || 'Telegram Script', icon: <Send className="w-6 h-6" />, path: '/dashboard/telegram-script-generator' },
    { name: t('sidebar.budget_updater') || 'Budget Updater', icon: <DollarSign className="w-6 h-6" />, path: '/dashboard/gads-budget-updater' },
    { name: t('sidebar.budget_monitor') || 'Budget Monitor', icon: <DollarSign className="w-6 h-6" />, path: '/dashboard/budget-monitor' },
    { name: t('sidebar.performance_max') || 'Performance Max', icon: <Maximize2 className="w-6 h-6" />, path: '/dashboard/performance-max' },
    { name: t('sidebar.device_bid') || 'Device Bid Adjuster', icon: <Smartphone className="w-6 h-6" />, path: '/dashboard/device-bid' }
  ];

  const dropdownIcons = [
    { name: t('sidebar.campaign_tools') || 'Campaign Tools', icon: <BarChart2 className="w-6 h-6" />, path: '#' },
    { name: t('sidebar.search_tools') || 'Search Tools', icon: <Settings className="w-6 h-6" />, path: '#' }
  ];

  return (
    <div className="h-full flex flex-col items-center bg-gray-900 text-white py-4 border-r border-gray-800">
      <button 
        onClick={toggleSidebar}
        className="p-2 rounded-md hover:bg-gray-800 transition-colors mb-4"
        title="Expand Sidebar"
      >
        <Zap className="w-6 h-6 text-blue-400" />
      </button>
      <nav className="flex flex-col space-y-3">
        {mainNavItemsCollapsed.map((item) => (
          <Link
            key={item.name}
            to={item.path}
            className={`p-2.5 rounded-lg transition-colors duration-200 ${location.pathname === item.path ? 'bg-blue-900/50 text-white' : 'text-gray-400 hover:bg-gray-800/50 hover:text-white'}`}
            title={item.name}
          >
            {item.icon}
          </Link>
        ))}
        {dropdownIcons.map((item) => (
           <div key={item.name} title={item.name} className="p-2.5 rounded-lg text-gray-400 hover:bg-gray-800/50 hover:text-white cursor-pointer">
            {item.icon}
          </div>
        ))}
      </nav>
    </div>
  );
};

// Extend the HTMLDivElement interface to include _sidebarRef
interface ExtendedHTMLDivElement extends HTMLDivElement {
  _sidebarRef?: HTMLDivElement;
}

const MainSidebar: React.FC = () => {
  const { isCollapsed } = useSidebar();

  const handleRef = (node: ExtendedHTMLDivElement | null) => {
    if (node) {
      node._sidebarRef = node;
    }
  };

  return (
    <div 
      ref={isCollapsed ? null : handleRef} 
      className={`fixed left-0 top-20 bottom-0 z-40 ${
        isCollapsed ? 'w-16' : 'w-64'
      } bg-gray-900/60 transition-all duration-300 ease-in-out border-r border-gray-700/50`}
    >
      <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-800">
        {isCollapsed ? <CollapsedSidebar /> : <Sidebar />}
      </div>
    </div>
  );
};

export default MainSidebar;
