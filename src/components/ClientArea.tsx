import React from 'react';
import { Link } from 'react-router-dom';
import { BarChart2, Table, Send, ArrowRight, Settings, DollarSign } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

// Tool paths for consistent routing
const TOOL_PATHS = {
  AIRTABLE_SCRIPT: 'airtable-script',
  TELEGRAM_SCRIPT: 'telegram-script-generator',
  BUDGET_UPDATER: 'gads-budget-updater',
  SEARCH_QUERY: 'search-query',
  PERFORMANCE_MAX: 'performance-max',
  KEYWORD_CONFLICT: 'keyword-conflict',
  SCRIPT_GENERATOR: 'script-generator',
  CAMPAIGN_PERFORMANCE: 'campaign-performance',
  AD_PERFORMANCE: 'ad-performance',
  KEYWORD_PERFORMANCE: 'keyword-performance',
  BUDGET_MONITOR: 'budget-monitor',
  DEVICE_BID: 'device-bid'
};

interface Tool {
  name: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  gradient: string;
}

const ClientArea: React.FC = () => {
  const { t, isLoading } = useLanguage();

  const tools: Tool[] = [
    {
      name: t('tools.airtable.name'),
      description: t('tools.airtable.description'),
      icon: <Table className="w-8 h-8 text-blue-400" />,
      path: TOOL_PATHS.AIRTABLE_SCRIPT,
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      name: t('tools.telegram.name'),
      description: t('tools.telegram.description'),
      icon: <Send className="w-8 h-8 text-blue-400" />,
      path: TOOL_PATHS.TELEGRAM_SCRIPT,
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      name: t('tools.budget.name'),
      description: t('tools.budget.description'),
      icon: <DollarSign className="w-8 h-8 text-blue-400" />,
      path: TOOL_PATHS.BUDGET_UPDATER,
      gradient: 'from-green-500 to-emerald-500',
    },
  ];

  const featureSections = [
    {
      title: t('dashboard.features.campaign.title') || 'Campaign Performance',
      icon: <BarChart2 size={40} />,
      description: t('dashboard.features.campaign.description') || 'Monitor and analyze your campaign performance across different dimensions.',
      links: [
        { text: t('dashboard.features.campaign.performance_by_type') || 'Campaign Performance by Type', path: TOOL_PATHS.CAMPAIGN_PERFORMANCE },
        { text: t('dashboard.features.campaign.ad_analyzer') || 'Ad Performance Analyzer', path: TOOL_PATHS.AD_PERFORMANCE },
        { text: t('dashboard.features.campaign.keyword_monitor') || 'Keyword Performance Monitor', path: TOOL_PATHS.KEYWORD_PERFORMANCE },
        { text: t('dashboard.features.campaign.search_analyzer') || 'Search Query Analyzer', path: TOOL_PATHS.SEARCH_QUERY },
      ],
    },
    {
      title: t('dashboard.features.budget.title') || 'Budget & Bidding',
      icon: <DollarSign size={40} />,
      description: t('dashboard.features.budget.description') || 'Optimize your campaign budgets and bidding strategies.',
      links: [
        { text: t('dashboard.features.budget.pace_monitor') || 'Budget Pace Monitor', path: TOOL_PATHS.BUDGET_MONITOR },
        { text: t('dashboard.features.budget.device_adjuster') || 'Device Bid Adjuster', path: TOOL_PATHS.DEVICE_BID },
        { text: t('dashboard.features.budget.performance_max') || 'Performance Max Asset Analyzer', path: TOOL_PATHS.PERFORMANCE_MAX },
      ],
    },
    {
      title: t('dashboard.features.advanced.title') || 'Advanced Tools',
      icon: <Settings size={40} />,
      description: t('dashboard.features.advanced.description') || 'Enhance your campaign with specialized tools and automation.',
      links: [
        {
          text: t('dashboard.features.advanced.script_generator') || 'Search Ads Script Generator',
          path: TOOL_PATHS.SCRIPT_GENERATOR,
          highlight: true
        },
        {
          text: t('dashboard.features.advanced.keyword_conflict') || 'Keyword Conflict Detector',
          path: TOOL_PATHS.KEYWORD_CONFLICT
        },
      ],
    },
  ];



  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white pt-5 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-blue-100/70">Loading translations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white pt-5">
      <div className="w-full mx-0 pl-2 pr-2 py-5">
        {/* Welcome Section */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
            {t('dashboard.welcome.title')}
          </h1>
          <p className="text-blue-100/70 text-lg max-w-3xl mx-auto">
            {t('dashboard.welcome.subtitle')}
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full mb-8">
          {tools.map((tool) => (
            <Link 
              key={tool.path} 
              to={`/dashboard/${tool.path}`}
              className="group block h-full"
            >
              <div className="h-full bg-gray-800/50 rounded-xl p-6 border border-gray-700/50 hover:border-blue-400/30 transition-colors flex flex-col">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${tool.gradient} flex items-center justify-center mb-4`}>
                  {tool.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-blue-300 transition-colors">
                  {tool.name}
                </h3>
                <p className="text-blue-100/60 mb-4 flex-grow">
                  {tool.description}
                </p>
                <div className="flex items-center text-blue-400 group-hover:text-blue-300 transition-colors">
                  <span>{t('dashboard.open_tool') || 'Open tool'}</span>
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Features Section */}
        <div className="mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">{t('dashboard.features.title') || 'Powerful Features, Simple Interface'}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featureSections.map((section, index) => (
              <div key={index} className="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
                <div className="text-blue-400 mb-4">
                  {section.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{section.title}</h3>
                <p className="text-blue-100/60 mb-4">{section.description}</p>
                <div className="space-y-2">
                  {section.links.map((link) => (
                    <Link
                      key={link.text}
                      to={`/dashboard/${link.path}`}
                      className={`block transition-colors ${
                        link.highlight 
                          ? 'text-amber-400 hover:text-amber-300 font-medium' 
                          : 'text-blue-400 hover:text-blue-300'
                      }`}
                      data-discover="true"
                    >
                      {link.text}
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientArea;
