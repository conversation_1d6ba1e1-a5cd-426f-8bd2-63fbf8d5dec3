import * as React from 'react';
import { useState, useCallback } from 'react';
import { HelpCircle, Info, Key, Settings } from 'lucide-react';

const LOCAL_STORAGE_PREFIX = 'airtablePnlScriptGenerator_';
const MAX_HISTORY_LENGTH = 3;

interface FormData {
  accountName: string;
  buyerId: string;
  niche: string;
  airtableUrl: string;
  apiKey: string;
  banDays: string;
  freezeDays: string;
  warningDays: string;
}

const AirtablePnlScriptGenerator: React.FC = () => {
  // State hooks
  const [formData, setFormData] = useState<FormData>(() => {
    const defaultValues: FormData = {
      accountName: '',
      buyerId: '',
      niche: '',
      airtableUrl: '',
      apiKey: '',
      banDays: '30',
      freezeDays: '15',
      warningDays: '7',
    };
    
    const initialFormData: Partial<FormData> = {};
    (Object.keys(defaultValues) as Array<keyof FormData>).forEach(key => {
      const storedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${key}_lastInput`);
      initialFormData[key] = storedValue !== null ? storedValue : defaultValues[key];
    });
    return initialFormData as FormData;
  });

  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: 'info' | 'success' | 'error' } | null>(null);

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Memoize updateFieldHistory to prevent unnecessary re-renders
  const updateFieldHistory = useCallback((field: keyof FormData, value: string) => {
    if (!value.trim()) return;
    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${field}_lastInput`, value);
    setFormHistory(prevHistory => {
      const currentHistory = prevHistory[field];
      const newHistory = [value, ...currentHistory.filter(item => item !== value)].slice(0, MAX_HISTORY_LENGTH);
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${field}_history`, JSON.stringify(newHistory));
      return { ...prevHistory, [field]: newHistory };
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target as { name: keyof FormData, value: string };
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const generateRandomString = (length = 0): string => {
    if (!length) length = Math.floor(Math.random() * 6) + 5;
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  const scriptTemplate = `function main() {
    try {
  var {{airName}} = '{{ACC_NAME_OR_MACHINE_ID}}'; 
  var {{airGroup}} = '{{BUYER_ID_FROM_AIRTABLE}}'; 
  var {{airTheme}} = '{{NICHE_FROM_AIRTABLE}}';  
  
  var {{airUrl}} = '{{URL_OF_AIRTABLE}}'; 
  var {{airKey}} = '{{API_OF_AIRTABLE}}'; 
  
  var {{statusBannedTerm}} = {{DAYS_TO_BAN}};
  var {{statusFrozenTerm}} = {{DAYS_TO_FREEZE}}; 
  var {{statusWarningTerm}} = {{DAYS_TO_WARNING}}; 
  
  var {{CTTT}} = AdsApp.currentAccount();
  
  var {{body}} = {
       'records':[    
        {
         'id': '',
         'fields':{
          'Status': 'Works',
          'Clicks (T)': {{CTTT}}.getStatsFor('TODAY').getClicks(),
          'Clicks (A)': {{CTTT}}.getStatsFor('ALL_TIME').getClicks(),
          'Impr. (T)': {{CTTT}}.getStatsFor('TODAY').getImpressions(),
          'Impr. (A)': {{CTTT}}.getStatsFor('ALL_TIME').getImpressions(),
          'CTR (T)': {{CTTT}}.getStatsFor('TODAY').getCtr(),
          'CTR (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCtr(),
          'CPC (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpc(),
          'CPC (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpc(),
          'Cost (T)': {{CTTT}}.getStatsFor('TODAY').getCost(),
          'Cost (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCost(),
          'CPM (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpm(),
          'CPM (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpm(),
          'Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions(),
          'Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions(),
          'Conv. Rate (T)': {{CTTT}}.getStatsFor('TODAY').getConversionRate(),
          'Conv. Rate (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversionRate(),
          'Cost/Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions() > 0 ? {{CTTT}}.getStatsFor('TODAY').getCost() / {{CTTT}}.getStatsFor('TODAY').getConversions() : 0,
          'Cost/Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions() > 0 ? {{CTTT}}.getStatsFor('ALL_TIME').getCost() / {{CTTT}}.getStatsFor('ALL_TIME').getConversions() : 0
         }
        }
       ]
    };
  
  var {{airSearchPattern}} = "AND({Account}='"+{{airName}}+"',{Group} ='"+{{airGroup}}+"')";
  
  var {{response}} = JSON.parse({{reqGet}}('GET', {{airUrl}}+"?filterByFormula="+encodeURI({{airSearchPattern}}), {{airKey}}).getContentText()); 

  if({{response}}['records'].length > 0){
    {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
    
    var {{fields}} = {{response}}['records'][0]['fields'];
    var {{dataDiff}} = {{dateComparison}}(({{fields}}['Last update'] || new Date().toISOString()), new Date().toISOString());
    var {{statusCurrent}} = {{fields}}['Status'];

    if({{body}}['records'][0]['fields']['Impr. (A)'] != ({{fields}}['Impr. (A)'] || 0) ){
      {{body}}['records'][0]['fields']['Status'] = 'Works';
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}})
    } else 
      if({{dataDiff}} > {{statusBannedTerm}} && {{statusCurrent}} != 'Banned'){
        {{body}}['records'][0]['fields'] = 
        { 
          'Status': 'Banned',
          'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
          'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
          'Last status change': new Date().toISOString()
        };
        {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
      } else 
        if({{dataDiff}} > {{statusFrozenTerm}} && {{statusCurrent}} != 'Frozen' && {{statusCurrent}} != 'Banned'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Frozen',
            'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
            'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
            'Last status change': new Date().toISOString()
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        } else 
        if({{dataDiff}} > {{statusWarningTerm}} && {{statusCurrent}} != 'Warning' && {{statusCurrent}} != 'Frozen' && {{statusCurrent}} != 'Banned'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Warning',
            'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
            'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
            'Last status change': new Date().toISOString()
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        }
  } else {
    var {{createBody}} = {
     'records':[ 
      {
       'fields':{
        'Account': {{airName}},
        'Group': {{airGroup}},
        'Theme': {{airTheme}},
        'Status': 'Works'
       }
      }
     ]
    };
  
  var createResponseVar = {{reqCustom}}('POST', {{airUrl}}, {{createBody}}, {{airKey}});
  if (createResponseVar.getResponseCode() >= 200 && createResponseVar.getResponseCode() < 300) {
    {{response}} = JSON.parse(createResponseVar.getContentText());
    if ({{response}} && {{response}}['records'] && {{response}}['records'].length > 0) {
      {{body}}['records'][0]['id'] = {{response}}['records'][0]['id'];
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
    } else {
      Logger.log("Error: No records returned from create operation for " + {{airName}});
    }
  } else {
    Logger.log("Error creating record for " + {{airName}} + ": " + createResponseVar.getResponseCode() + " - " + createResponseVar.getContentText());
  }
  }
  
  Logger.log("Script executed successfully for account: " + {{airName}});
} catch(e) {
  Logger.log("Error occurred in main function for account " + (typeof {{airName}} !== 'undefined' ? {{airName}} : 'unknown') + ": " + e.message + " Stack: " + e.stack);
}
}

function {{reqGet}}(method, url, key){
    try {
      var {{header}} = {
        authorization: 'Bearer ' + key
      };
      const {{options}} = {
          method: method,
          headers: {{header}},
          muteHttpExceptions: true
      };
      var response = UrlFetchApp.fetch(url, {{options}});
      if (response.getResponseCode() >= 400) {
        Logger.log("API GET Error: " + response.getResponseCode() + " - " + response.getContentText() + " URL: " + url);
      }
      return response;
    } catch(e) {
      Logger.log("Request GET error: " + e.message + " URL: " + url);
      throw e;
    }
}

function {{reqCustom}}(method, url, body, key){
  try {
    var {{header}} = {
      authorization: 'Bearer ' + key
    }
    var {{options}} = {
      method : method,
      headers: {{header}},
      contentType: 'application/json',
      payload: JSON.stringify(body),
      muteHttpExceptions: true
    };
    var response = UrlFetchApp.fetch(url, {{options}});
    if (response.getResponseCode() >= 400) {
      Logger.log("API " + method + " Error: " + response.getResponseCode() + " - " + response.getContentText() + " URL: " + url);
    }
    return response;
  } catch(e) {
    Logger.log("Request " + method + " error: " + e.message + " URL: " + url);
    throw e;
  }
}

function {{dateComparison}}(date1, date2){
  var d1 = new Date(date1);
  var d2 = new Date(date2);
  var timeDiff = Math.abs(d2.getTime() - d1.getTime());
  var {{diffDays}} = Math.ceil(timeDiff / (1000 * 3600 * 24)); 
  return {{diffDays}};
}`;

  // Memoize the script generation function
  const generateUniqueAirtableScript = useCallback((currentFormData: FormData): string => {
    let uniqueScript = scriptTemplate;
    const varMap: Record<string, string> = {};
    const placeholders = ['airName', 'airGroup', 'airTheme', 'airUrl', 'airKey', 'CTTT', 'body', 
                        'dateComparison', 'response', 'diffDays', 'statusFrozenTerm', 'statusBannedTerm', 
                        'statusWarningTerm', 'reqGet', 'reqCustom', 'airSearchPattern', 'fields', 'dataDiff', 
                        'statusCurrent', 'createBody', 'header', 'options'];
    
    // Replace placeholders with random strings
    placeholders.forEach(ph => {
      const length = ph.length > 8 ? Math.floor(Math.random() * 4) + 8 : Math.floor(Math.random() * 3) + 5;
      varMap[ph] = generateRandomString(length);
      uniqueScript = uniqueScript.replace(new RegExp('{{' + ph + '}}', 'g'), varMap[ph]);
    });
    
    // Replace form data placeholders
    uniqueScript = uniqueScript
      .replace('{{ACC_NAME_OR_MACHINE_ID}}', currentFormData.accountName.replace(/'/g, "\\'"))
      .replace('{{BUYER_ID_FROM_AIRTABLE}}', currentFormData.buyerId.replace(/'/g, "\\'"))
      .replace('{{NICHE_FROM_AIRTABLE}}', currentFormData.niche.replace(/'/g, "\\'"))
      .replace('{{URL_OF_AIRTABLE}}', currentFormData.airtableUrl.replace(/'/g, "\\'"))
      .replace('{{API_OF_AIRTABLE}}', currentFormData.apiKey.replace(/'/g, "\\'"))
      .replace(/{{DAYS_TO_BAN}}/g, parseInt(currentFormData.banDays, 10).toString())
      .replace(/{{DAYS_TO_FREEZE}}/g, parseInt(currentFormData.freezeDays, 10).toString())
      .replace(/{{DAYS_TO_WARNING}}/g, parseInt(currentFormData.warningDays, 10).toString());
      
    return uniqueScript;
  }, [scriptTemplate]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);
    const requiredFields: Array<keyof FormData> = ['accountName', 'buyerId', 'niche', 'airtableUrl', 'apiKey', 'banDays', 'freezeDays', 'warningDays'];
    
    // Validate required fields
    for (const field of requiredFields) {
      if (!formData[field]) {
        setMessage({ 
          text: `Please fill in the '${inputFieldConfig[field].label.replace(':','')}' field.`, 
          type: 'error' 
        });
        return;
      }
    }

    // Validate numeric fields
    const numericValidations = [
      { field: 'banDays', message: 'Days to Ban must be a positive number.' },
      { field: 'freezeDays', message: 'Days to Freeze must be a positive number.' },
      { field: 'warningDays', message: 'Days to Warning must be a positive number.' }
    ];

    for (const { field, message } of numericValidations) {
      const value = parseInt(formData[field as keyof FormData]);
      if (isNaN(value) || value <= 0) {
        setMessage({ text: message, type: 'error' });
        return;
      }
    }

    // Generate and set script
    const script = generateUniqueAirtableScript(formData);
    setGeneratedScript(script);
    setShowResult(true);

    // Update field history
    (Object.keys(formData) as Array<keyof FormData>).forEach(key => {
      updateFieldHistory(key, formData[key]);
    });

    // Scroll to result section
    setTimeout(() => {
      document.getElementById('resultSectionAirtablePnl')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  }, [formData, inputFieldConfig, generateUniqueAirtableScript]);

  // Define the input field configuration type
  type InputFieldConfig = {
    [K in keyof FormData]: {
      label: string;
      placeholder: string;
      type?: string;
      info: string;
      icon: React.ElementType;
    };
  };

  // Memoize inputFieldConfig to prevent unnecessary re-renders
  const inputFieldConfig = React.useMemo<InputFieldConfig>(() => ({
    accountName: { 
      label: 'Account Name / Machine ID:', 
      placeholder: 'Enter account identifier', 
      info: 'Unique name to identify the account in Airtable.', 
      icon: Info 
    },
    buyerId: { 
      label: 'Media Buyer ID from Airtable:', 
      placeholder: 'e.g., buyer123', 
      info: 'Identifier of the buyer group in Airtable.', 
      icon: Info 
    },
    niche: { 
      label: 'Niche from Airtable:', 
      placeholder: 'e.g., CRPT, ECOM', 
      info: 'Account theme or category for reporting.', 
      icon: Info 
    },
    airtableUrl: { 
      label: 'Airtable URL:', 
      placeholder: 'https://api.airtable.com/v0/appXXXXXXXX/tblXXXXXXX', 
      info: 'Full API URL to your Airtable table (including table ID or name).', 
      icon: Key 
    },
    apiKey: { 
      label: 'Airtable API Key:', 
      placeholder: 'keyXXXXXXXXXXXXXX', 
      info: 'Your personal Airtable API key for authentication.', 
      icon: Key, 
      type: 'password' 
    },
    warningDays: { 
      label: 'Days to Warning:', 
      placeholder: 'e.g., 7', 
      type: 'number', 
      info: 'Inactive days before status: "Warning".', 
      icon: Settings 
    },
    freezeDays: { 
      label: 'Days to Freeze:', 
      placeholder: 'e.g., 15', 
      type: 'number', 
      info: 'Inactive days before status: "Frozen".', 
      icon: Settings 
    },
    banDays: { 
      label: 'Days to Ban:', 
      placeholder: 'e.g., 30', 
      type: 'number', 
      info: 'Inactive days before status: "Banned".', 
      icon: Settings 
    },
  }), []);

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);
    
    // Define required fields and their validation
    const requiredFields: Array<keyof FormData> = ['accountName', 'buyerId', 'niche', 'airtableUrl', 'apiKey', 'banDays', 'freezeDays', 'warningDays'];
    const numericFields = ['banDays', 'freezeDays', 'warningDays'] as const;
    
    // Validate required fields
    for (const field of requiredFields) {
      if (!formData[field]) {
        const fieldLabel = field === 'accountName' ? 'Account Name / Machine ID' :
                         field === 'buyerId' ? 'Media Buyer ID' :
                         field === 'niche' ? 'Niche' :
                         field === 'airtableUrl' ? 'Airtable URL' :
                         field === 'apiKey' ? 'API Key' :
                         field === 'banDays' ? 'Days to Ban' :
                         field === 'freezeDays' ? 'Days to Freeze' : 'Days to Warning';
        
        setMessage({ 
          text: `Please fill in the '${fieldLabel}' field.`, 
          type: 'error' 
        });
        return;
      }
    }

    // Validate numeric fields
    for (const field of numericFields) {
      const value = parseInt(formData[field]);
      if (isNaN(value) || value <= 0) {
        const fieldName = field === 'banDays' ? 'Days to Ban' :
                         field === 'freezeDays' ? 'Days to Freeze' : 'Days to Warning';
        setMessage({ 
          text: `${fieldName} must be a positive number.`,
          type: 'error' 
        });
        return;
      }
    }

    try {
      // Generate and set script
      const script = generateUniqueAirtableScript(formData);
      setGeneratedScript(script);
      setShowResult(true);

      // Update field history
      (Object.keys(formData) as Array<keyof FormData>).forEach(key => {
        updateFieldHistory(key, formData[key]);
      });

      // Scroll to result section
      setTimeout(() => {
        const resultSection = document.getElementById('resultSectionAirtablePnl');
        if (resultSection) {
          resultSection.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      setMessage({ 
        text: 'An error occurred while generating the script. Please try again.',
        type: 'error' 
      });
      console.error('Error generating script:', error);
    }
  }, [formData, updateFieldHistory, generateUniqueAirtableScript]);


    for (const field of numericFields) {
      const value = parseInt(formData[field]);
      if (isNaN(value) || value <= 0) {
        const fieldName = field === 'banDays' ? 'Days to Ban' :
                         field === 'freezeDays' ? 'Days to Freeze' : 'Days to Warning';
        setMessage({ 
          text: `${fieldName} must be a positive number.`,
          type: 'error' 
        });
        return;
      }
    }

    try {
      // Generate and set script
      const script = generateUniqueAirtableScript(formData);
      setGeneratedScript(script);
      setShowResult(true);

      // Update field history
      (Object.keys(formData) as Array<keyof FormData>).forEach(key => {
        updateFieldHistory(key, formData[key]);
      });

      // Scroll to result section
      setTimeout(() => {
        const resultSection = document.getElementById('resultSectionAirtablePnl');
        if (resultSection) {
          resultSection.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      setMessage({ 
        text: 'An error occurred while generating the script. Please try again.',
        type: 'error' 
    <div className="bg-sky-50 p-4 rounded-md border border-sky-200 mt-4 mb-6 text-sm">
      <h3 className="font-semibold text-sky-700 mb-2">Script Preview:</h3>
      <ul className="list-disc list-inside space-y-1 text-neutral-700">
        <li>Connects to Airtable URL: <span className='font-medium'>{formData.airtableUrl || '(Not set)'}</span></li>
        <li>Uses API Key: <span className='font-medium'>{formData.apiKey ? '********' : '(Not set)'}</span></li>
        <li>Identifies account as: <span className='font-medium'>"{formData.accountName || '(Account Name)'}"</span></li>
        <li>Identifies buyer group as: <span className='font-medium'>"{formData.buyerId || '(Buyer ID)'}"</span></li>
        <li>Categorizes under niche: <span className='font-medium'>"{formData.niche || '(Niche)'}"</span></li>
        <li>Updates performance metrics (clicks, impressions, cost, conversions, etc.)</li>
        <li>Changes status to "Warning" after <span className='font-medium'>{formData.warningDays || '(X)'}</span> days of inactivity</li>
        <li>Changes status to "Frozen" after <span className='font-medium'>{formData.freezeDays || '(Y)'}</span> days of inactivity</li>
        <li>Changes status to "Banned" after <span className='font-medium'>{formData.banDays || '(Z)'}</span> days of inactivity</li>
        <li>Creates a new record if the account/group combination doesn't exist</li>
      </ul>
    </div>
  );
};

const handleCopyScript = useCallback(() => {
  navigator.clipboard.writeText(generatedScript)
    .then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    })
    .catch(err => {
      console.error('Failed to copy script: ', err);
      setMessage({ text: 'Failed to copy script. Please copy manually.', type: 'error' });
    });
}, [generatedScript]);
    navigator.clipboard.writeText(generatedScript)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('Failed to copy script: ', err);
        setMessage({ text: 'Failed to copy script. Please copy manually.', type: 'error' });
      });
  }, [generatedScript]);

  const renderInputField = (fieldId: keyof FormData) => {
    const config = inputFieldConfig[fieldId];
    return (
      <div key={fieldId}>
        <label htmlFor={fieldId} className="block text-sm font-medium text-neutral-700 mb-1">
          {config.label}
          <span className="relative group ml-1">
            <HelpCircle size={16} className="text-neutral-500 cursor-help inline" />
            <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 z-20 w-72 bg-neutral-700 text-white text-xs p-2 rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
              {config.info}
              <span className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-l-transparent border-r-4 border-r-transparent border-t-4 border-t-neutral-700"></span>
            </span>
          </span>
        </label>
        <input
          type={config.type || 'text'}
          id={fieldId}
          name={fieldId}
          value={formData[fieldId]}
          onChange={handleInputChange}
          onBlur={() => updateFieldHistory(fieldId, formData[fieldId])} 
          className="w-full p-2 border border-neutral-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500"
          placeholder={config.placeholder}
          list={`${LOCAL_STORAGE_PREFIX}${fieldId}_history`}
          min={config.type === 'number' ? "1" : undefined}
        />
        <datalist id={`${LOCAL_STORAGE_PREFIX}${fieldId}_history`}>
          {formHistory[fieldId]?.map((item, index) => (
            <option key={index} value={item} />
          ))}
        </datalist>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6">
      {!showResult ? (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-sky-50 p-5 rounded-lg shadow-inner border-2 border-sky-200">
            <h4 className="text-lg font-semibold text-sky-700 mb-4 flex items-center">
                <Table size={22} className="mr-2" />
                Airtable Account Details
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {renderInputField('accountName')}
              {renderInputField('buyerId')}
              {renderInputField('niche')}
            </div>
          </div>

          <div className="bg-yellow-50 p-5 rounded-lg shadow-inner border-2 border-yellow-200">
            <h4 className="text-lg font-semibold text-yellow-700 mb-4 flex items-center">
                <Key size={22} className="mr-2" />
                Airtable Connection
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {renderInputField('airtableUrl')}
              {renderInputField('apiKey')}
            </div>
          </div>

          <div className="bg-rose-50 p-5 rounded-lg shadow-inner border-2 border-rose-200">
            <h4 className="text-lg font-semibold text-rose-700 mb-4 flex items-center">
                <Settings size={22} className="mr-2" />
                Status Automation Rules
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
              {renderInputField('warningDays')}
              {renderInputField('freezeDays')}
              {renderInputField('banDays')}
            </div>
          </div>
          
          <div className="flex items-center mb-2 mt-4">
            <input
              type="checkbox"
              id="showPreviewAirtablePnl"
              checked={showPreview}
              onChange={() => setShowPreview(!showPreview)}
              className="h-4 w-4 text-sky-600 focus:ring-sky-500 border-neutral-300 rounded"
            />
            <label htmlFor="showPreviewAirtablePnl" className="ml-2 block text-sm text-neutral-700">
              Show script action preview
            </label>
          </div>
          {showPreview && generateScriptPreview()}

          {message && (
            <div
              className={`p-3 my-4 text-sm rounded-lg ${
                message.type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
                message.type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
                'bg-blue-100 border border-blue-400 text-blue-700'
              }`}
              role="alert"
            >
              <span className="font-medium">{message.type.charAt(0).toUpperCase() + message.type.slice(1)}:</span> {message.text}
            </div>
          )}

          <button 
            type="submit"
            className="w-full bg-sky-600 text-white font-semibold py-3 px-4 rounded-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 transition-colors"
          >
            Generate Script
          </button>
        </form>
      ) : (
        <div id="resultSectionAirtablePnl">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral-800">Your Airtable P&L Script</h2>
            <button 
              onClick={handleCopyScript}
              className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors flex items-center 
                          ${copySuccess 
                            ? 'bg-green-100 text-green-700 focus:ring-green-500'
                            : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200 focus:ring-sky-500'}`}
            >
              {copySuccess ? <Check size={16} className="mr-1.5" /> : <Copy size={16} className="mr-1.5" />}
              {copySuccess ? 'Copied!' : 'Copy Script'}
            </button>
          </div>
          <pre className="bg-neutral-800 text-neutral-200 p-4 rounded-md overflow-auto h-[400px] text-xs">
            <code>{generatedScript}</code>
          </pre>
          <button 
            onClick={() => {setShowResult(false); setMessage(null); setCopySuccess(false);}}
            className="mt-4 w-full bg-sky-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 transition-colors"
            > 
            Back to Form
          </button>
        </div>
      )}

      {/* Instructions and Help Sections */} 
      <div className="mt-8 space-y-6">
        <div className="bg-white p-5 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-800 mb-3 flex items-center"><ListChecks size={20} className="mr-2 text-sky-600"/>How to Use the Script</h3>
          <ol className="list-decimal list-inside space-y-1.5 text-sm text-neutral-700">
            <li>Copy the generated script using the button above.</li>
            <li>Open your Google Ads account and navigate to "Tools & Settings" &gt; "Bulk Actions" &gt; "Scripts".</li>
            <li>Click the "+" button to create a new script. Give it a descriptive name.</li>
            <li>Paste the copied code into the editor.</li>
            <li>Authorize the script (it will ask for permissions to manage your campaigns and access external services).</li>
            <li>Set up a schedule for the script to run (e.g., hourly or daily is recommended for timely updates).</li>
            <li>Save the script. You can run it manually first or wait for the schedule.</li>
          </ol>
          <p className="mt-3 text-xs text-neutral-600">
            <span className="font-semibold">Note:</span> Ensure your Airtable base has columns matching the fields the script updates (e.g., 'Status', 'Clicks (T)', 'Impr. (A)', 'Last update', etc.).
          </p>
        </div>

        <div className="bg-white p-5 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-800 mb-3 flex items-center"><BookOpen size={20} className="mr-2 text-sky-600"/>How to Get Airtable Connection Details</h3>
          <div className="space-y-3 text-sm text-neutral-700">
            <div>
              <h4 className="font-medium">Airtable URL (Table specific):</h4>
              <p>Log in to Airtable &gt; Open your base &gt; Click "Help" (top right) &gt; "API documentation". In the API docs, select your target table on the left. The URL will be shown in the examples, typically like: <code>https://api.airtable.com/v0/YOUR_APP_ID/YOUR_TABLE_NAME_OR_ID</code>. Use this full URL.</p>
            </div>
            <div>
              <h4 className="font-medium">Airtable API Key:</h4>
              <p>Go to your Airtable account page (<a href="https://airtable.com/account" target="_blank" rel="noopener noreferrer" className="text-sky-600 hover:underline">https://airtable.com/account</a>) &gt; Find the "API" section &gt; Generate or use an existing API key. It usually starts with <code>key</code>.</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-5 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-neutral-800 mb-3 flex items-center"><Info size={20} className="mr-2 text-sky-600"/>Automatically Populated Fields</h3>
            <p className="mb-3 text-sm text-neutral-700">When the script changes an account's status to Warning, Frozen, or Banned due to inactivity, it also attempts to populate these fields in your Airtable:</p>
            <div className="bg-neutral-50 p-3 rounded-md border border-neutral-200 text-xs space-y-2">
              <div>
                <p><span className="font-medium">Ad status:</span> Approval status of the first ad found (e.g., "APPROVED", "DISAPPROVED"). Field type: Text.</p>
              </div>
              <div>
                <p><span className="font-medium">Disapproved reason:</span> Any disapproval reasons for the first ad. Field type: Long text.</p>
              </div>
              <div>
                <p><span className="font-medium">Last status change:</span> Timestamp of when the status was changed by the script. Field type: Date/Time.</p>
              </div>
            </div>
             <p className="mt-2 text-xs text-neutral-600">Ensure these fields exist in your Airtable table if you want this data.</p>
        </div>
      </div>
    </div>
  );
};

export default AirtablePnlScriptGenerator;
