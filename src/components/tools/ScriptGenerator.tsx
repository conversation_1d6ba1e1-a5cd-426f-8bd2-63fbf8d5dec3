import React, { useState, useMemo, useCallback } from 'react';
import {
  <PERSON><PERSON>s, Bot, Newspaper, Tags, Trash2, PlusCircle, PlayCircle,
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

interface Ad {
  id: string;
  headlines: string[];
  descriptions: string[];
}

interface Keyword {
  id: string;
  text: string;
}

interface ScriptData {
  finalUrl: string;
  campaignName: string;
  adGroupName: string;
  budget: number;
  ads: Array<{ headlines: string[]; descriptions: string[] }>;
  keywords: string[];
  useTelegram: boolean;
  telegramBotToken?: string;
  telegramChatId?: string;
}

const ScriptGenerator: React.FC = () => {
  const { t } = useLanguage();

  const [finalUrl, setFinalUrl] = useState<string>('https://example.com');
  const [campaignName, setCampaignName] = useState<string>('My Search Campaign');
  const [adGroupName, setAdGroupName] = useState<string>('My Ad Group');
  const [budget, setBudget] = useState<number>(50);
  const [ads, setAds] = useState<Ad[]>([
    { id: `ad-${Date.now()}` , headlines: ['Main Headline 1', 'Main Headline 2', 'Main Headline 3'], descriptions: ['Description Line 1', 'Description Line 2'] }
  ]);
  const [keywords, setKeywords] = useState<Keyword[]>([{ id: `kw-${Date.now()}`, text: 'example keyword' }]);
  const [enableTelegram, setEnableTelegram] = useState<boolean>(false);
  const [telegramBotToken, setTelegramBotToken] = useState<string>('');
  const [telegramChatId, setTelegramChatId] = useState<string>('');
  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [message, setMessage] = useState<{ text: string | React.ReactNode, type: 'success' | 'error' | 'info' | 'warning' } | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const activeAds = useMemo(() => ads.filter(ad => ad.headlines.some(h => h.trim()) || ad.descriptions.some(d => d.trim())), [ads]);
  const activeKeywords = useMemo(() => keywords.filter(kw => kw.text.trim()), [keywords]);

  const generateActualScript = (data: ScriptData): string => {
    const uniqueSuffix = `_u${new Date().getTime().toString(36)}`;

    const tsCampaignName = data.campaignName;
    const tsAdGroupName = data.adGroupName;
    const tsFinalUrl = data.finalUrl;
    const tsBudget = data.budget;
    
    // Process keywords to determine text and match type
    const processedKeywordsForScript = (data.keywords.length > 0 ? data.keywords : ["sample keyword"])
      .map(kwText => {
        let text = kwText.trim();
        let matchType = 'Broad'; // Default match type

        if (text.startsWith('[') && text.endsWith(']')) {
          matchType = 'Exact';
          text = text.substring(1, text.length - 1).trim(); // Get content inside brackets
        } else if (text.startsWith('"') && text.endsWith('"')) {
          matchType = 'Phrase';
          text = text.substring(1, text.length - 1).trim(); // Get content inside quotes
        }
        // For Broad match, text is used as-is after initial trim.

        return { text: text, matchType: matchType };
      })
      .filter(kw => kw.text.length > 0); // Remove any keywords that became empty after stripping brackets/quotes

    const tsUseTelegram = data.useTelegram;
    const tsTelegramBotToken = data.telegramBotToken || '';
    const tsTelegramChatId = data.telegramChatId || '';
    const tsAdType = 'Responsive search ad'; 

    let script = '';

    if (tsUseTelegram && tsTelegramBotToken && tsTelegramChatId) {
      script += `
function sendTelegramMessage${uniqueSuffix}(message) {
  var url = "https://api.telegram.org/bot" + "${tsTelegramBotToken}" + "/sendMessage?chat_id=" + "${tsTelegramChatId}" + "&text=" + encodeURIComponent(message) + "&parse_mode=Markdown";
  try {
    UrlFetchApp.fetch(url);
  } catch (e) {
    Logger.log("Failed to send Telegram message: " + e.toString());
  }
}
`;
    }

    script += `
function main() {
  var accountName${uniqueSuffix} = AdsApp.currentAccount().getName();
  var accountId${uniqueSuffix} = AdsApp.currentAccount().getCustomerId();
  var timezone${uniqueSuffix} = AdsApp.currentAccount().getTimeZone();
  var currentDateTime${uniqueSuffix} = Utilities.formatDate(new Date(), timezone${uniqueSuffix}, "yyyy-MM-dd HH:mm:ss z");

  var startMessage${uniqueSuffix} = "🚀 Script Execution Started\n" +
                                 "------------------------------------\n" +
                                 "📊 Script: Bulk Upload for Campaign '${tsCampaignName}'\n" +
                                 "🏭 Account: " + accountName${uniqueSuffix} + " (" + accountId${uniqueSuffix} + ")\n" +
                                 "📅 Date: " + currentDateTime${uniqueSuffix} + "\n" +
                                 "------------------------------------";
  if (${tsUseTelegram}) sendTelegramMessage${uniqueSuffix}(startMessage${uniqueSuffix});
  Logger.log("Starting bulk upload process for campaign: ${tsCampaignName}. Account: " + accountName${uniqueSuffix} + " (" + accountId${uniqueSuffix} + ")");

  try {
    var scriptCsvColumns${uniqueSuffix} = [
      'Campaign',
      'Budget',
      'Bid Strategy type',
      'Campaign type',
      'Ad group',
      'Keyword',
      'Criterion type',
      'Status', // Added for keyword status
      'Headline 1', 'Headline 2', 'Headline 3', 'Headline 4','Headline 5','Headline 6','Headline 7','Headline 8','Headline 9','Headline 10','Headline 11','Headline 12','Headline 13','Headline 14','Headline 15',
      'Description 1', 'Description 2', 'Description 3', 'Description 4',
      'Final URL',
      'Ad Type'
    ];

    var upload${uniqueSuffix} = AdsApp.bulkUploads().newCsvUpload(
      scriptCsvColumns${uniqueSuffix}, 
      {moneyInMicros: false}
    );

    upload${uniqueSuffix}.append({
      'Campaign': "${tsCampaignName}", 
      'Budget': ${tsBudget}, 
      'Bid Strategy type': 'Maximize Clicks', 
      'Campaign type': 'Search' 
    });

    upload${uniqueSuffix}.append({
      'Campaign': "${tsCampaignName}",
      'Ad group': "${tsAdGroupName}"
    });

    var keywordsData${uniqueSuffix} = ${JSON.stringify(processedKeywordsForScript)};
    for (var i${uniqueSuffix} = 0; i${uniqueSuffix} < keywordsData${uniqueSuffix}.length; i${uniqueSuffix}++) {
      var kwObj${uniqueSuffix} = keywordsData${uniqueSuffix}[i${uniqueSuffix}];
      upload${uniqueSuffix}.append({
        'Campaign': "${tsCampaignName}",
        'Ad group': "${tsAdGroupName}",
        'Keyword': kwObj${uniqueSuffix}.text,
        'Criterion type': kwObj${uniqueSuffix}.matchType,
        'Status': 'Enabled' // Set keyword status to Enabled
      });
    }

    var adDataForUpload${uniqueSuffix} = {};
    adDataForUpload${uniqueSuffix}['Campaign'] = "${tsCampaignName}";
    adDataForUpload${uniqueSuffix}['Ad group'] = "${tsAdGroupName}";
    adDataForUpload${uniqueSuffix}['Final URL'] = "${tsFinalUrl}";
    adDataForUpload${uniqueSuffix}['Ad Type'] = "${tsAdType}";

    var userAdHeadlines${uniqueSuffix} = ${JSON.stringify(data.ads[0].headlines)};
    for (var h_idx${uniqueSuffix} = 0; h_idx${uniqueSuffix} < userAdHeadlines${uniqueSuffix}.length; h_idx${uniqueSuffix}++) {
      if (h_idx${uniqueSuffix} < 15) { 
        adDataForUpload${uniqueSuffix}['Headline ' + (h_idx${uniqueSuffix} + 1)] = userAdHeadlines${uniqueSuffix}[h_idx${uniqueSuffix}];
      }
    }
  
    var userAdDescriptions${uniqueSuffix} = ${JSON.stringify(data.ads[0].descriptions)};
    for (var d_idx${uniqueSuffix} = 0; d_idx${uniqueSuffix} < userAdDescriptions${uniqueSuffix}.length; d_idx${uniqueSuffix}++) {
      if (d_idx${uniqueSuffix} < 4) { 
        adDataForUpload${uniqueSuffix}['Description ' + (d_idx${uniqueSuffix} + 1)] = userAdDescriptions${uniqueSuffix}[d_idx${uniqueSuffix}];
      }
    }
    upload${uniqueSuffix}.append(adDataForUpload${uniqueSuffix});

    upload${uniqueSuffix}.apply();
    Logger.log("Bulk upload applied successfully for campaign: ${tsCampaignName}.");

    var successMessage${uniqueSuffix} = "✅ Script Execution Finished Successfully\n" +
                                       "------------------------------------\n" +
                                       "📊 Script: Bulk Upload for Campaign '${tsCampaignName}'\n" +
                                       "🏭 Account: " + accountName${uniqueSuffix} + " (" + accountId${uniqueSuffix} + ")\n" +
                                       "📅 Date: " + currentDateTime${uniqueSuffix} + "\n" +
                                       "🎉 Campaign, Ad Group, Ads, and Keywords should be processed.\n" +
                                       "------------------------------------";
    if (${tsUseTelegram}) sendTelegramMessage${uniqueSuffix}(successMessage${uniqueSuffix});

  } catch (e) {
    Logger.log("Error during bulk upload for campaign ${tsCampaignName}: " + e.toString());
    var errorMessage${uniqueSuffix} = "❌ Script Execution Failed\n" +
                                    "------------------------------------\n" +
                                    "📊 Script: Bulk Upload for Campaign '${tsCampaignName}'\n" +
                                    "🏭 Account: " + accountName${uniqueSuffix} + " (" + accountId${uniqueSuffix} + ")\n" +
                                    "📅 Date: " + currentDateTime${uniqueSuffix} + "\n" +
                                    "🔥 Error: " + e.message + "\n" +
                                    "------------------------------------";
    if (${tsUseTelegram}) sendTelegramMessage${uniqueSuffix}(errorMessage${uniqueSuffix});
  }
}
`;

    return script;
  };

  const handleGenerateScript = () => {
    setIsGenerating(true);
    setMessage(null);

    if (!campaignName.trim() || !adGroupName.trim() || !finalUrl.trim()) {
      setMessage({ text: t('tools.scriptGenerator.errors.requiredFields'), type: 'error' });
      setIsGenerating(false);
      return;
    }
    if (budget <= 0) {
      setMessage({ text: t('tools.scriptGenerator.errors.invalidBudget'), type: 'error' });
      setIsGenerating(false);
      return;
    }
    try { 
      new URL(finalUrl); 
    } catch {
      setMessage({ text: t('tools.scriptGenerator.errors.invalidUrl'), type: 'error' });
      setIsGenerating(false);
      return;
    }
    if (enableTelegram && (!telegramBotToken.trim() || !telegramChatId.trim())) {
      setMessage({ text: t('tools.scriptGenerator.errors.telegramRequired'), type: 'error' });
      setIsGenerating(false);
      return;
    }

    if (activeAds.length === 0) {
        setMessage({ text: t('tools.scriptGenerator.errors.noAds'), type: 'error' });
        setIsGenerating(false);
        return;
    }
    if (activeAds.some(ad => ad.headlines.filter(h => h.trim()).length === 0 || ad.descriptions.filter(d => d.trim()).length === 0)) {
        setMessage({ text: t('tools.scriptGenerator.errors.incompleteAds'), type: 'error' });
        setIsGenerating(false);
        return;
    }
    
    if (activeKeywords.length === 0) {
        setMessage({ text: t('tools.scriptGenerator.errors.noKeywords'), type: 'error' });
        setIsGenerating(false);
        return;
    }

    const scriptData: ScriptData = {
        finalUrl,
        campaignName,
        adGroupName,
        budget,
        ads: activeAds.map(ad => ({ headlines: ad.headlines.filter(h => h.trim()), descriptions: ad.descriptions.filter(d => d.trim()) })),
        keywords: activeKeywords.map(kw => kw.text.trim()).filter(kw => kw),
        useTelegram: enableTelegram,
        telegramBotToken: enableTelegram ? telegramBotToken : undefined,
        telegramChatId: enableTelegram ? telegramChatId : undefined,
    };
    
    setTimeout(() => {
      const script = generateActualScript(scriptData);
      setGeneratedScript(script);
      setMessage({ text: t('tools.scriptGenerator.success.generated'), type: 'success'});
      setIsGenerating(false);
    }, 500);
  };

  const handleAdChange = useCallback((id: string, type: 'headline' | 'description', index: number, value: string) => {
    setAds(prevAds => prevAds.map(ad => {
      if (ad.id === id) {
        if (type === 'headline') {
          const newHeadlines = [...ad.headlines];
          newHeadlines[index] = value;
          return { ...ad, headlines: newHeadlines };
        } else {
          const newDescriptions = [...ad.descriptions];
          newDescriptions[index] = value;
          return { ...ad, descriptions: newDescriptions };
        }
      }
      return ad;
    }));
  }, []);

  const handleAddElement = useCallback((adId: string, type: 'headline' | 'description') => {
    setAds(prevAds => prevAds.map(ad => {
      if (ad.id === adId) {
        if (type === 'headline' && ad.headlines.length < 15) {
          return { ...ad, headlines: [...ad.headlines, ''] };
        }
        if (type === 'description' && ad.descriptions.length < 4) {
          return { ...ad, descriptions: [...ad.descriptions, ''] };
        }
      }
      return ad;
    }));
  }, []);

  const handleRemoveElement = useCallback((adId: string, type: 'headline' | 'description', index: number) => {
    setAds(prevAds => prevAds.map(ad => {
      if (ad.id === adId) {
        if (type === 'headline') {
          const newHeadlines = ad.headlines.filter((_, i) => i !== index);
          return { ...ad, headlines: newHeadlines };
        }
        if (type === 'description') {
          const newDescriptions = ad.descriptions.filter((_, i) => i !== index);
          return { ...ad, descriptions: newDescriptions };
        }
      }
      return ad;
    }));
  }, []);

  const handleAddKeyword = () => {
    setKeywords([...keywords, { id: `kw-${Date.now()}`, text: '' }]);
  };

  const handleRemoveKeyword = (id: string) => {
    setKeywords(keywords.filter(kw => kw.id !== id));
  };

  const handleKeywordChange = (id: string, value: string) => {
    setKeywords(keywords.map(kw => kw.id === id ? { ...kw, text: value } : kw));
  };

  const howThisWorksContent = (
    <ul className="list-disc list-inside space-y-1.5">
      <li>{t('tools.scriptGenerator.howItWorks.step1')}</li>
      <li>{t('tools.scriptGenerator.howItWorks.step2')}</li>
      <li>{t('tools.scriptGenerator.howItWorks.step3')}</li>
      <li>{t('tools.scriptGenerator.howItWorks.step4')}</li>
      <li>{t('tools.scriptGenerator.howItWorks.step5')}</li>
    </ul>
  );

  return (
    <ToolPageLayout
      title={t('tools.scriptGenerator.title')}
      description={t('tools.scriptGenerator.description')}
      howThisWorksContent={howThisWorksContent}
    >
      <div className="space-y-8">
        {message && (
          <NotificationMessage 
            type={message.type} 
            message={message.text} 
            onDismiss={() => setMessage(null)} 
          />
        )}

        <FormSection title={t('tools.scriptGenerator.sections.campaignDetails')} icon={<Settings />} theme="sky">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormItem label={t('tools.scriptGenerator.fields.finalUrl')} htmlFor="finalUrl" tooltipText={t('tools.scriptGenerator.tooltips.finalUrl')} required>
              <StyledInput type="text" id="finalUrl" value={finalUrl} onChange={(e) => setFinalUrl(e.target.value)} placeholder="https://yourwebsite.com" themeColor="sky" />
            </FormItem>
            <FormItem label={t('tools.scriptGenerator.fields.campaignName')} htmlFor="campaignName" tooltipText={t('tools.scriptGenerator.tooltips.campaignName')} required>
              <StyledInput type="text" id="campaignName" value={campaignName} onChange={(e) => setCampaignName(e.target.value)} placeholder="e.g., Summer Sale Campaign" themeColor="sky" />
            </FormItem>
            <FormItem label={t('tools.scriptGenerator.fields.adGroupName')} htmlFor="adGroupName" tooltipText={t('tools.scriptGenerator.tooltips.adGroupName')} required>
              <StyledInput type="text" id="adGroupName" value={adGroupName} onChange={(e) => setAdGroupName(e.target.value)} placeholder="e.g., Main Product Ad Group" themeColor="sky" />
            </FormItem>
            <FormItem label={t('tools.scriptGenerator.fields.dailyBudget')} htmlFor="budget" tooltipText={t('tools.scriptGenerator.tooltips.dailyBudget')} required>
              <StyledInput type="number" id="budget" value={budget} onChange={(e) => setBudget(parseFloat(e.target.value) || 0)} placeholder="50" themeColor="sky" />
            </FormItem>
          </div>
        </FormSection>

        <FormSection title={t('tools.scriptGenerator.sections.adContent')} icon={<Newspaper />} theme="emerald">
          {ads.slice(0, 1).map((ad) => (
            <div key={ad.id} className="p-4 border border-slate-700 rounded-lg bg-slate-800/30">
              <div className="mb-6">
                <label className="block text-sm font-medium text-sky-300 mb-2">{t('tools.scriptGenerator.fields.headlines')}</label>
                {ad.headlines.map((headline, hIndex) => (
                  <div key={`ad-${ad.id}-h-${hIndex}`} className="flex items-center space-x-2 mb-2">
                    <StyledInput
                      type="text"
                      value={headline}
                      onChange={(e) => handleAdChange(ad.id, 'headline', hIndex, e.target.value)}
                      placeholder={`${t('tools.scriptGenerator.placeholders.headline')} ${hIndex + 1}`}
                      maxLength={30}
                      themeColor='emerald'
                    />
                    <StyledButton 
                        variant="icon"
                        onClick={() => handleRemoveElement(ad.id, 'headline', hIndex)} 
                        disabled={ad.headlines.length <= 1}
                        className={`${ad.headlines.length <= 1 ? 'opacity-50 cursor-not-allowed' : ''} text-red-400 hover:text-red-300`}
                        tooltipText={t('tools.scriptGenerator.tooltips.removeHeadline')}
                    >
                      <Trash2 size={18} />
                    </StyledButton>
                  </div>
                ))}
                {ad.headlines.length < 15 && (
                  <StyledButton variant="ghost" onClick={() => handleAddElement(ad.id, 'headline')} leftIcon={<PlusCircle size={16}/>} className="mt-1 text-sky-400 hover:text-sky-300 text-xs">
                    {t('tools.scriptGenerator.buttons.addHeadline')}
                  </StyledButton>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-sky-300 mb-2">{t('tools.scriptGenerator.fields.descriptions')}</label>
                {ad.descriptions.map((desc, dIndex) => (
                  <div key={`ad-${ad.id}-d-${dIndex}`} className="flex items-center space-x-2 mb-2">
                    <StyledInput
                      type="text"
                      value={desc}
                      onChange={(e) => handleAdChange(ad.id, 'description', dIndex, e.target.value)}
                      placeholder={`${t('tools.scriptGenerator.placeholders.description')} ${dIndex + 1}`}
                      maxLength={90}
                      themeColor='emerald'
                    />
                     <StyledButton 
                        variant="icon"
                        onClick={() => handleRemoveElement(ad.id, 'description', dIndex)} 
                        disabled={ad.descriptions.length <= 1}
                        className={`${ad.descriptions.length <= 1 ? 'opacity-50 cursor-not-allowed' : ''} text-red-400 hover:text-red-300`}
                        tooltipText={t('tools.scriptGenerator.tooltips.removeDescription')}
                    >
                      <Trash2 size={18} />
                    </StyledButton>
                  </div>
                ))}
                {ad.descriptions.length < 4 && (
                  <StyledButton variant="ghost" onClick={() => handleAddElement(ad.id, 'description')} leftIcon={<PlusCircle size={16}/>} className="mt-1 text-sky-400 hover:text-sky-300 text-xs">
                    {t('tools.scriptGenerator.buttons.addDescription')}
                  </StyledButton>
                )}
              </div>
            </div>
          ))}
        </FormSection>

        <FormSection title={t('tools.scriptGenerator.sections.keywords')} icon={<Tags />} theme="amber">
          {keywords.map((keyword, kwIndex) => (
            <div key={keyword.id} className="flex items-end gap-2 mb-3 p-3 bg-amber-900/30 border border-amber-700/50 rounded-md">
              <FormItem label={`${t('tools.scriptGenerator.fields.keyword')} ${kwIndex + 1}`} htmlFor={`keyword-${keyword.id}`} className="flex-grow">
                <StyledInput 
                  type="text" 
                  id={`keyword-${keyword.id}`} 
                  value={keyword.text} 
                  onChange={(e) => handleKeywordChange(keyword.id, e.target.value)} 
                  placeholder={t('tools.scriptGenerator.placeholders.keyword')}
                  themeColor="amber"
                />
              </FormItem>
              <StyledButton variant="destructive" size="icon" onClick={() => handleRemoveKeyword(keyword.id)} themeColor='red' aria-label={t('tools.scriptGenerator.tooltips.removeKeyword')}>
                <Trash2 className="h-4 w-4" />
              </StyledButton>
            </div>
          ))}
          <StyledButton variant="secondary" onClick={handleAddKeyword} leftIcon={<PlusCircle />} themeColor='amber'>{t('tools.scriptGenerator.buttons.addKeyword')}</StyledButton>
        </FormSection>

        <FormSection title={t('tools.scriptGenerator.sections.telegram')} icon={<Bot />} theme="purple">
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="enableTelegram"
                  name="enableTelegram"
                  checked={enableTelegram}
                  onChange={(e) => setEnableTelegram(e.target.checked)}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${enableTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {enableTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                {t('tools.scriptGenerator.fields.enableTelegram')}
              </span>
            </label>
          </div>
          {enableTelegram && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <FormItem label={t('tools.scriptGenerator.fields.telegramBotToken')} htmlFor="telegramBotToken" tooltipText={t('tools.scriptGenerator.tooltips.telegramBotToken')}>
                <StyledInput type="password" id="telegramBotToken" value={telegramBotToken} onChange={(e) => setTelegramBotToken(e.target.value)} placeholder="123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" themeColor="purple" />
              </FormItem>
              <FormItem label={t('tools.scriptGenerator.fields.telegramChatId')} htmlFor="telegramChatId" tooltipText={t('tools.scriptGenerator.tooltips.telegramChatId')}>
                <StyledInput type="text" id="telegramChatId" value={telegramChatId} onChange={(e) => setTelegramChatId(e.target.value)} placeholder="-1001234567890 or 123456789" themeColor="purple" />
              </FormItem>
            </div>
          )}
        </FormSection>

        <div className="mt-10 pt-6 border-t border-slate-700">
          <StyledButton 
            onClick={handleGenerateScript} 
            isLoading={isGenerating}
            size="lg"
            leftIcon={<PlayCircle />}
            themeColor='sky'
            className='w-full md:w-auto'
          >
            {isGenerating ? t('common.generating') : t('tools.scriptGenerator.buttons.generateScript')}
          </StyledButton>
        </div>

        {generatedScript && (
          <ScriptDisplay scriptContent={generatedScript} title={t('tools.scriptGenerator.results.title')} />
        )}
      </div>
    </ToolPageLayout>
  );
};

export default ScriptGenerator;
