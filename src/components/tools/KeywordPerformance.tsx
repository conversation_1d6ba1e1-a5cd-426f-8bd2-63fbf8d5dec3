import React, { useState, useEffect } from 'react';
import { BarC<PERSON>2, <PERSON>Text, Settings, ChevronDown, ChevronUp, TrendingUp, Send } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';
import { TooltipProvider } from '@/components/ui/tooltip'; // Added for FormItem tooltips

const LOCAL_STORAGE_PREFIX = 'keywordPerformance_';

const getGoogleAdsDateRangesKP = (t: (key: string) => string) => [
  { value: 'TODAY', label: t('common.dateRanges.today') },
  { value: 'YESTERDAY', label: t('common.dateRanges.yesterday') },
  { value: 'LAST_7_DAYS', label: t('common.dateRanges.last7Days') },
  { value: 'LAST_14_DAYS', label: t('common.dateRanges.last14Days') },
  { value: 'LAST_30_DAYS', label: t('common.dateRanges.last30Days') },
  { value: 'LAST_90_DAYS', label: t('common.dateRanges.last90Days') },
  { value: 'THIS_MONTH', label: t('common.dateRanges.thisMonth') },
  { value: 'LAST_MONTH', label: t('common.dateRanges.lastMonth') },
];

const KeywordPerformance: React.FC = () => {
  const { t } = useLanguage();

  // Form inputs
  const [campaignName, setCampaignName] = useState('');
  const [minImpressions, setMinImpressions] = useState('1000');
  const [minClicks, setMinClicks] = useState('100');
  const [targetCTR, setTargetCTR] = useState('2');
  const [maxCPC, setMaxCPC] = useState('5');
  const [minQualityScore, setMinQualityScore] = useState('5');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [spreadsheetUrl, setSpreadsheetUrl] = useState('');
  const [sheetName, setSheetName] = useState('Keyword Report');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  // const [copySuccess, setCopySuccess] = useState(false); // Handled by ScriptDisplay
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    const fields = [
      { key: 'campaignName', setter: setCampaignName, type: 'string' },
      { key: 'minImpressions', setter: setMinImpressions, type: 'string' },
      { key: 'minClicks', setter: setMinClicks, type: 'string' },
      { key: 'targetCTR', setter: setTargetCTR, type: 'string' },
      { key: 'maxCPC', setter: setMaxCPC, type: 'string' },
      { key: 'minQualityScore', setter: setMinQualityScore, type: 'string' },
      { key: 'dateRange', setter: setDateRange, type: 'string' },
      { key: 'spreadsheetUrl', setter: setSpreadsheetUrl, type: 'string' },
      { key: 'sheetName', setter: setSheetName, type: 'string' },
      { key: 'useTelegram', setter: setUseTelegram, type: 'boolean' },
      { key: 'telegramBotToken', setter: setTelegramBotToken, type: 'string' },
      { key: 'telegramChatId', setter: setTelegramChatId, type: 'string' },
      { key: 'showAdvanced', setter: setShowAdvanced, type: 'boolean' }, // Persist advanced toggle state
    ];

    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field.key}`);
      if (savedValue !== null) {
        if (field.type === 'boolean') {
          (field.setter as React.Dispatch<React.SetStateAction<boolean>>)(savedValue === 'true');
        } else {
          (field.setter as React.Dispatch<React.SetStateAction<string>>)(savedValue);
        }
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const valToSet = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    if (name === 'useTelegram') {
      const boolValue = valToSet as boolean;
      setUseTelegram(boolValue);
      setShowAdvanced(boolValue); // This controls the visibility of Telegram settings
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(boolValue));
      
      if (!boolValue) {
        setTelegramBotToken('');
        setTelegramChatId('');
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
      }
    } else {
      const stringValue = valToSet as string;
      switch(name) {
        case 'campaignName':
          setCampaignName(stringValue);
          break;
        case 'minImpressions':
          setMinImpressions(stringValue);
          break;
        case 'minClicks':
          setMinClicks(stringValue);
          break;
        case 'targetCTR':
          setTargetCTR(stringValue);
          break;
        case 'maxCPC':
          setMaxCPC(stringValue);
          break;
        case 'minQualityScore':
          setMinQualityScore(stringValue);
          break;
        case 'dateRange':
          setDateRange(stringValue);
          break;
        case 'spreadsheetUrl':
          setSpreadsheetUrl(stringValue);
          break;
        case 'sheetName':
          setSheetName(stringValue);
          break;
        case 'telegramBotToken':
          setTelegramBotToken(stringValue);
          break;
        case 'telegramChatId':
          setTelegramChatId(stringValue);
          break;
      }
      
      if (name) {
        localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, stringValue);
      }
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);
    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: t('tools.keywordPerformance.errors.telegramRequired'), type: 'error' });
      return;
    }
    if (spreadsheetUrl && !sheetName) {
      setMessage({ text: t('tools.keywordPerformance.errors.sheetNameRequired'), type: 'error' });
      return;
    }
    // Basic validation
    if (isNaN(parseFloat(minImpressions)) || isNaN(parseFloat(minClicks)) || isNaN(parseFloat(targetCTR)) || isNaN(parseFloat(maxCPC)) || isNaN(parseInt(minQualityScore))) {
        setMessage({ text: t('tools.keywordPerformance.errors.invalidNumbers'), type: 'error' });
        return;
    }

    const script = generateKeywordPerformanceScript(
      campaignName,
      parseInt(minImpressions, 10),
      parseInt(minClicks, 10),
      parseFloat(targetCTR) / 100, 
      parseFloat(maxCPC),
      parseInt(minQualityScore, 10),
      dateRange,
      spreadsheetUrl,
      sheetName,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('resultSectionKeywordPerf')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateKeywordPerformanceScript = (
    campaignNameVal: string, minImpressionsVal: number, minClicksVal: number, targetCTRVal: number,
    maxCPCVal: number, minQualityScoreVal: number, dateRangeVal: string,
    spreadsheetUrlVal: string, sheetNameVal: string,
    useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'kwPerf' + Date.now().toString(36) + '_';
    let telegramCode = '';
    if (useTelegramVal) {
      telegramCode = `
function ${uniquePrefix}sendTelegramNotification(message) {
  var ${uniquePrefix}payload = {
    'chat_id': '${chatId.replace(/'/g, "\\'")}',
    'text': message,
    'parse_mode': 'HTML'
  };
  var ${uniquePrefix}options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(${uniquePrefix}payload)
  };
  try {
    UrlFetchApp.fetch('https://api.telegram.org/bot${botToken.replace(/'/g, "\\'")}/sendMessage', ${uniquePrefix}options);
    return true;
  } catch (e) { 
    Logger.log('Error sending Telegram notification: ' + e); 
    return false;
  }
}
`;
    }

    let spreadsheetCode = '';
    if (spreadsheetUrlVal) {
      spreadsheetCode = `
  var ${uniquePrefix}SPREADSHEET_URL = '${spreadsheetUrlVal.replace(/'/g, "\\'")}';
  var ${uniquePrefix}SHEET_NAME = '${sheetNameVal.replace(/'/g, "\\'")}';
  var ${uniquePrefix}ss = SpreadsheetApp.openByUrl(${uniquePrefix}SPREADSHEET_URL);
  var ${uniquePrefix}sheet = ${uniquePrefix}ss.getSheetByName(${uniquePrefix}SHEET_NAME);
  if (!${uniquePrefix}sheet) {
    ${uniquePrefix}sheet = ${uniquePrefix}ss.insertSheet(${uniquePrefix}SHEET_NAME);
  }
  ${uniquePrefix}sheet.clearContents(); // Clear previous data
  ${uniquePrefix}sheet.appendRow(['Campaign', 'Ad Group', 'Keyword', 'Match Type', 'Impressions', 'Clicks', 'CTR', 'Avg. CPC', 'Cost', 'Quality Score', 'Status', 'Suggestion']);

  function logToSheet(data) {
    ${uniquePrefix}sheet.appendRow(data);
  }
`;
    }

    const mainScript = `
function main() {
  var ${uniquePrefix}CONFIG = {
    CAMPAIGN_NAME_CONTAINS: '${campaignNameVal.replace(/'/g, "\\'")}',
    MIN_IMPRESSIONS: ${minImpressionsVal},
    MIN_CLICKS: ${minClicksVal},
    TARGET_CTR: ${targetCTRVal}, // e.g., 0.02 for 2%
    MAX_CPC: ${maxCPCVal}, // e.g., 5 for $5
    MIN_QUALITY_SCORE: ${minQualityScoreVal},
    DATE_RANGE: '${dateRangeVal}'
  };

  var ${uniquePrefix}reportSummary = [];
  var ${uniquePrefix}keywordsFlagged = 0;

  Logger.log('Script starting with configuration: ' + JSON.stringify(${uniquePrefix}CONFIG));
  ${useTelegramVal ? `${uniquePrefix}sendTelegramNotification('<b>📊 Keyword Performance Script Started</b>' + '\\n' + 'Campaign filter: ' + (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS ? ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS : 'ALL CAMPAIGNS'));` : ''}
  ${spreadsheetUrlVal ? `logToSheet(['Keyword Performance Report - ' + new Date().toLocaleDateString(), '', '', '', '', '', '', '', '', '', '', '']);` : '' }

  var ${uniquePrefix}keywordSelector = AdsApp.keywords()
    .withCondition("KeywordMatchType IN ['BROAD', 'PHRASE', 'EXACT']")
    .withCondition('Status = ENABLED')
    .withCondition('CampaignStatus = ENABLED')
    .withCondition('AdGroupStatus = ENABLED');

  if (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS !== '') {
    ${uniquePrefix}keywordSelector = ${uniquePrefix}keywordSelector.withCondition(
      "CampaignName CONTAINS_IGNORE_CASE '" + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + "'"
    );
  }

  var ${uniquePrefix}keywordIterator = ${uniquePrefix}keywordSelector.forDateRange(${uniquePrefix}CONFIG.DATE_RANGE).get();

  while (${uniquePrefix}keywordIterator.hasNext()) {
    var ${uniquePrefix}keyword = ${uniquePrefix}keywordIterator.next();
    var ${uniquePrefix}stats = ${uniquePrefix}keyword.getStatsFor(${uniquePrefix}CONFIG.DATE_RANGE);

    if (${uniquePrefix}stats.getImpressions() < ${uniquePrefix}CONFIG.MIN_IMPRESSIONS || ${uniquePrefix}stats.getClicks() < ${uniquePrefix}CONFIG.MIN_CLICKS) {
      continue;
    }

    var ${uniquePrefix}ctr = ${uniquePrefix}stats.getCtr();
    var ${uniquePrefix}avgCpc = ${uniquePrefix}stats.getAverageCpc();
    var ${uniquePrefix}qualityScore = ${uniquePrefix}keyword.getQualityScore();
    var ${uniquePrefix}suggestion = [];

    if (${uniquePrefix}ctr < ${uniquePrefix}CONFIG.TARGET_CTR) {
      ${uniquePrefix}suggestion.push('Low CTR: ' + (${uniquePrefix}ctr * 100).toFixed(2) + '% (Target: >' + (${uniquePrefix}CONFIG.TARGET_CTR * 100).toFixed(2) + '%)');
    }
    if (${uniquePrefix}avgCpc > ${uniquePrefix}CONFIG.MAX_CPC) {
      ${uniquePrefix}suggestion.push('High CPC: $' + ${uniquePrefix}avgCpc.toFixed(2) + ' (Max: $' + ${uniquePrefix}CONFIG.MAX_CPC.toFixed(2) + ')');
    }
    if (${uniquePrefix}qualityScore !== null && ${uniquePrefix}qualityScore < ${uniquePrefix}CONFIG.MIN_QUALITY_SCORE) {
      ${uniquePrefix}suggestion.push('Low QS: ' + ${uniquePrefix}qualityScore + ' (Min: ' + ${uniquePrefix}CONFIG.MIN_QUALITY_SCORE + ')');
    }
    if ((${uniquePrefix}stats.getImpressions() > 0 && ${uniquePrefix}stats.getConversions() == 0) || (${uniquePrefix}stats.getImpressions() == 0 && ${uniquePrefix}stats.getCost() > 0)) {
        ${uniquePrefix}suggestion.push('Zero Conversions with spend or Zero Impressions with spend/activity.');
    }


    if (${uniquePrefix}suggestion.length > 0) {
      ${uniquePrefix}keywordsFlagged++;
      var ${uniquePrefix}logData = [
        ${uniquePrefix}keyword.getCampaign().getName(),
        ${uniquePrefix}keyword.getAdGroup().getName(),
        ${uniquePrefix}keyword.getText(),
        ${uniquePrefix}keyword.getKeywordMatchType(),
        ${uniquePrefix}stats.getImpressions(),
        ${uniquePrefix}stats.getClicks(),
        (${uniquePrefix}ctr * 100).toFixed(2) + '%',
        ${uniquePrefix}avgCpc.toFixed(2),
        ${uniquePrefix}stats.getCost().toFixed(2),
        ${uniquePrefix}qualityScore,
        ${uniquePrefix}keyword.getStatus(),
        ${uniquePrefix}suggestion.join('; ')
      ];
      Logger.log('Flagged Keyword: ' + ${uniquePrefix}logData.join(', '));
      ${spreadsheetUrlVal ? `logToSheet(${uniquePrefix}logData);` : '' }
      ${uniquePrefix}reportSummary.push(${uniquePrefix}keyword.getText() + ': ' + ${uniquePrefix}suggestion.join('; '));
    }
  }

  Logger.log('Script finished. Total Keywords Flagged: ' + ${uniquePrefix}keywordsFlagged);
  var ${uniquePrefix}summaryMessage = 'Keyword Performance Script Finished. Flagged ' + ${uniquePrefix}keywordsFlagged + ' keywords for review.';
  if (${uniquePrefix}keywordsFlagged > 0 && ${uniquePrefix}reportSummary.length > 0) {
    ${uniquePrefix}summaryMessage += '\\n\\nFirst few flagged: \\n - ' + ${uniquePrefix}reportSummary.slice(0,5).join('\\n - ');
  }
  
  if (${useTelegramVal}) {
    var ${uniquePrefix}account = AdsApp.currentAccount();
    var ${uniquePrefix}accountName = ${uniquePrefix}account.getName();
    var ${uniquePrefix}accountId = ${uniquePrefix}account.getCustomerId();
    var ${uniquePrefix}timestamp = Utilities.formatDate(new Date(), ${uniquePrefix}account.getTimeZone(), 'yyyy-MM-dd HH:mm:ss z');
    
    var ${uniquePrefix}message = '✅ Script Execution Finished Successfully' + '\\n' +
                   '------------------------------------' + '\\n' +
                   '📊 Script: Keyword Performance Analyzer' + '\\n' +
                   '🏭 Account: ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')' + '\\n' +
                   '📅 Date: ' + ${uniquePrefix}timestamp + '\\n' +
                   '📈 Results: ' + ${uniquePrefix}keywordsFlagged + ' keywords flagged for review' + '\\n' +
                   '------------------------------------';
    
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}message);
  }
  ${spreadsheetUrlVal ? `logToSheet(['','','','','','','','','','','End of Report', new Date().toLocaleString()]);` : '' }

}
`;
    return `${telegramCode}\n${spreadsheetCode}\n${mainScript}`;
  };

  return (
    <TooltipProvider>
    <ToolPageLayout
      title={t('tools.keywordPerformance.title')}
      description={t('tools.keywordPerformance.description')}
    >
      {message && (
        <NotificationMessage
          type={message.type}
          message={message.text}
          onDismiss={() => setMessage(null)}
          className="mb-6"
        />
      )}

      <form onSubmit={(e) => { e.preventDefault(); handleGenerateScript(); }} className="space-y-8">
        <FormSection title={t('tools.keywordPerformance.sections.campaignSettings')} icon={<Settings />} theme="blue">
          <FormItem label={t('tools.keywordPerformance.fields.campaignName')} htmlFor="campaignNameKP" tooltipText={t('tools.keywordPerformance.fields.campaignName')}>
            <StyledInput id="campaignNameKP" name="campaignName" value={campaignName} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.campaignNamePlaceholder')} />
          </FormItem>
          <FormItem label={t('tools.keywordPerformance.fields.dateRange')} htmlFor="dateRangeKP" tooltipText={t('tools.keywordPerformance.fields.dateRange')} required>
            <select 
              id="dateRangeKP" 
              name="dateRange" 
              value={dateRange} 
              onChange={handleInputChange}
              className="form-input block w-full rounded-md bg-slate-700/50 border-slate-600 text-gray-100 placeholder-gray-400 shadow-sm transition-colors duration-150 ease-in-out focus:border-transparent focus:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-sky-500 border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2 text-sm"
              style={{ 
                appearance: 'none',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'right 0.75rem center',
                backgroundSize: '0.8em 0.8em',
                paddingRight: '2.5rem',
                backgroundImage: "url('data:image/svg+xml;charset=UTF-8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2212%22 height=%2212%22 viewBox=%220 0 12 12%22 fill=%22none%22%3E%3Cpath d=%22M2.25 4.5L6 8.25L9.75 4.5%22 stroke=%22%23a0aec0%22 stroke-width=%221.5%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22/%3E%3C/svg%3E')"
              }}
            >
              {getGoogleAdsDateRangesKP(t).map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </FormItem>
        </FormSection>

        {/* Collapsible Performance Thresholds Section */}
        <div className={`p-5 sm:p-6 rounded-xl shadow-2xl border transition-colors duration-200 ease-in-out bg-emerald-950/70 hover:bg-emerald-900/80 border-emerald-700/70`}>
          <div 
            className="flex items-center justify-between text-xl sm:text-2xl font-semibold text-emerald-300 mb-4 sm:mb-6 cursor-pointer"
            onClick={() => {
              const newState = !showAdvanced;
              setShowAdvanced(newState);
              localStorage.setItem(`${LOCAL_STORAGE_PREFIX}showAdvanced`, String(newState));
            }}
          >
            <div className="flex items-center">
              <BarChart2 className={`h-6 w-6 sm:h-7 sm:w-7 mr-2.5 sm:mr-3 text-emerald-400 flex-shrink-0`} />
              {t('tools.keywordPerformance.sections.thresholds')}
            </div>
            {showAdvanced ? <ChevronUp className="h-6 w-6 text-emerald-400" /> : <ChevronDown className="h-6 w-6 text-emerald-400" />}
          </div>

          {showAdvanced && (
            <div className="space-y-4">
              <FormItem label={t('tools.keywordPerformance.fields.minImpressions')} htmlFor="minImpressionsKP" tooltipText={t('tools.keywordPerformance.fields.minImpressions')}>
                <StyledInput id="minImpressionsKP" name="minImpressions" type="number" value={minImpressions} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.minImpressionsPlaceholder')} />
              </FormItem>
              <FormItem label={t('tools.keywordPerformance.fields.minClicks')} htmlFor="minClicksKP" tooltipText={t('tools.keywordPerformance.fields.minClicks')}>
                <StyledInput id="minClicksKP" name="minClicks" type="number" value={minClicks} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.minClicksPlaceholder')} />
              </FormItem>
              <FormItem label={t('tools.keywordPerformance.fields.targetCTR')} htmlFor="targetCTRKP" tooltipText={t('tools.keywordPerformance.fields.targetCTR')}>
                <StyledInput id="targetCTRKP" name="targetCTR" type="number" step="0.1" value={targetCTR} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.targetCTRPlaceholder')} />
              </FormItem>
              <FormItem label={t('tools.keywordPerformance.fields.maxCPC')} htmlFor="maxCPCKP" tooltipText={t('tools.keywordPerformance.fields.maxCPC')}>
                <StyledInput id="maxCPCKP" name="maxCPC" type="number" step="0.01" value={maxCPC} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.maxCPCPlaceholder')} />
              </FormItem>
              <FormItem label={t('tools.keywordPerformance.fields.minQualityScore')} htmlFor="minQualityScoreKP" tooltipText={t('tools.keywordPerformance.fields.minQualityScore')}>
                <StyledInput id="minQualityScoreKP" name="minQualityScore" type="number" min="1" max="10" value={minQualityScore} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.minQualityScorePlaceholder')} />
              </FormItem>
            </div>
          )}
        </div>

        <FormSection title={t('tools.keywordPerformance.sections.reporting')} icon={<FileText />} theme="purple">
          <FormItem label={t('tools.keywordPerformance.fields.spreadsheetUrl')} htmlFor="spreadsheetUrlKP" tooltipText={t('tools.keywordPerformance.fields.spreadsheetUrl')}>
            <StyledInput id="spreadsheetUrlKP" name="spreadsheetUrl" value={spreadsheetUrl} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.spreadsheetUrlPlaceholder')} />
          </FormItem>
          <FormItem label={t('tools.keywordPerformance.fields.sheetName')} htmlFor="sheetNameKP" tooltipText={t('tools.keywordPerformance.fields.sheetName')}>
            <StyledInput id="sheetNameKP" name="sheetName" value={sheetName} onChange={handleInputChange} placeholder={t('tools.keywordPerformance.fields.sheetNamePlaceholder')} />
          </FormItem>
        </FormSection>

        <FormSection title={t('tools.keywordPerformance.sections.notifications')} icon={<Send />} theme="sky">
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="useTelegramKP"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${useTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {useTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                {t('common.enableTelegram')}
              </span>
            </label>
          </div>
          {useTelegram && (
            <>
              <FormItem label={t('tools.keywordPerformance.fields.telegramToken')} htmlFor="telegramBotTokenKP" tooltipText={t('tools.keywordPerformance.fields.telegramToken')} required={useTelegram}>
                <StyledInput id="telegramBotTokenKP" name="telegramBotToken" type="password" value={telegramBotToken} onChange={handleInputChange} placeholder={t('common.telegramTokenPlaceholder')} />
              </FormItem>
              <FormItem label={t('tools.keywordPerformance.fields.telegramChatId')} htmlFor="telegramChatIdKP" tooltipText={t('tools.keywordPerformance.fields.telegramChatId')} required={useTelegram}>
                <StyledInput id="telegramChatIdKP" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder={t('common.telegramChatIdPlaceholder')} />
              </FormItem>
            </>
          )}
        </FormSection>
        
        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <StyledButton type="submit" variant="primary" size="lg" themeColor="emerald" leftIcon={<TrendingUp className="mr-2 h-5 w-5" />}>
            {t('common.analyzeAndGenerate')}
          </StyledButton>
        </div>
      </form>

      {showResult && generatedScript && (
        <div id="resultSectionKeywordPerf" className="mt-10">
          <ScriptDisplay scriptContent={generatedScript} title={t('tools.keywordPerformance.generated')} />
        </div>
      )}
    </ToolPageLayout>
    </TooltipProvider>
  );
};

export default KeywordPerformance;
