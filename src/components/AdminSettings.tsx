import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { StyledTextarea as Textarea } from './ui/shared/StyledTextarea';
import { Settings, Users, BarChart3, Globe, Search, Edit, Save, Plus, Eye, Shield } from 'lucide-react';
import { api } from '../services/api';

interface User {
  id: string;
  email: string;
  role: string;
  is_active: boolean;
  created_at: string;
  last_login_at: string | null;
  access_expiry_date: string | null;
}

interface ContentKey {
  id: string;
  key_name: string;
  category: string;
  description: string;
  translations: {
    en: string;
    ua: string;
  };
}

interface SEOData {
  page: string;
  title_en: string;
  title_ua: string;
  description_en: string;
  description_ua: string;
  keywords_en: string;
  keywords_ua: string;
  verification_codes: string;
}

const AdminSettings: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('tracking');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Tracking & Analytics State
  const [trackingCode, setTrackingCode] = useState('');
  const [trackingPreview, setTrackingPreview] = useState('');

  // SEO Management State
  const [seoData, setSeoData] = useState<SEOData>({
    page: 'home',
    title_en: '',
    title_ua: '',
    description_en: '',
    description_ua: '',
    keywords_en: '',
    keywords_ua: '',
    verification_codes: ''
  });
  const [selectedPage, setSelectedPage] = useState('home');

  // User Management State
  const [users, setUsers] = useState<User[]>([]);
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    role: 'user',
    access_expiry_date: ''
  });
  const [editingUser, setEditingUser] = useState<string | null>(null);

  // CMS State
  const [contentKeys, setContentKeys] = useState<ContentKey[]>([]);
  const [filteredContent, setFilteredContent] = useState<ContentKey[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [editingContent, setEditingContent] = useState<string | null>(null);

  // Check admin access
  if (!user || user.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <Shield className="w-16 h-16 mx-auto mb-4 text-red-500" />
            <h2 className="text-xl font-semibold mb-2">{t('admin.access_denied')}</h2>
            <p className="text-gray-600">{t('admin.admin_only')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadTrackingCode(),
        loadSEOData(),
        loadUsers(),
        loadContentKeys()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      setMessage({ type: 'error', text: t('admin.load_error') });
    } finally {
      setLoading(false);
    }
  };

  const loadTrackingCode = async () => {
    try {
      const response = await api.get('/admin/tracking-code');
      setTrackingCode(response.data.code || '');
    } catch (error) {
      console.error('Error loading tracking code:', error);
    }
  };

  const loadSEOData = async () => {
    try {
      const response = await api.get(`/admin/seo/${selectedPage}`);
      setSeoData(response.data);
    } catch (error) {
      console.error('Error loading SEO data:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await api.get('/admin/users');
      setUsers(response.data);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const loadContentKeys = async () => {
    try {
      const response = await api.get('/admin/content-keys');
      setContentKeys(response.data);
      setFilteredContent(response.data);
    } catch (error) {
      console.error('Error loading content keys:', error);
    }
  };

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const validateJavaScript = (code: string): boolean => {
    try {
      new Function(code);
      return true;
    } catch (error) {
      return false;
    }
  };

  const saveTrackingCode = async () => {
    if (trackingCode && !validateJavaScript(trackingCode)) {
      showMessage('error', t('admin.invalid_javascript'));
      return;
    }

    setLoading(true);
    try {
      await api.post('/admin/tracking-code', { code: trackingCode });
      showMessage('success', t('admin.tracking_saved'));
    } catch (error) {
      showMessage('error', t('admin.save_error'));
    } finally {
      setLoading(false);
    }
  };

  const saveSEOData = async () => {
    if (seoData.description_en.length > 160 || seoData.description_ua.length > 160) {
      showMessage('error', t('admin.description_too_long'));
      return;
    }

    setLoading(true);
    try {
      await api.post('/admin/seo', seoData);
      showMessage('success', t('admin.seo_saved'));
    } catch (error) {
      showMessage('error', t('admin.save_error'));
    } finally {
      setLoading(false);
    }
  };

  const createUser = async () => {
    if (!newUser.email || !newUser.password) {
      showMessage('error', t('admin.fill_required_fields'));
      return;
    }

    setLoading(true);
    try {
      await api.post('/admin/users', newUser);
      setNewUser({ email: '', password: '', role: 'user', access_expiry_date: '' });
      await loadUsers();
      showMessage('success', t('admin.user_created'));
    } catch (error) {
      showMessage('error', t('admin.create_user_error'));
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (userId: string, updates: Partial<User>) => {
    setLoading(true);
    try {
      await api.put(`/admin/users/${userId}`, updates);
      await loadUsers();
      setEditingUser(null);
      showMessage('success', t('admin.user_updated'));
    } catch (error) {
      showMessage('error', t('admin.update_user_error'));
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (keyId: string, translations: { en: string; ua: string }) => {
    setLoading(true);
    try {
      await api.put(`/admin/content/${keyId}`, { translations });
      await loadContentKeys();
      setEditingContent(null);
      showMessage('success', t('admin.content_updated'));
    } catch (error) {
      showMessage('error', t('admin.update_content_error'));
    } finally {
      setLoading(false);
    }
  };

  const filterContent = () => {
    let filtered = contentKeys;

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.key_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.translations.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.translations.ua.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    setFilteredContent(filtered);
  };

  useEffect(() => {
    filterContent();
  }, [searchTerm, selectedCategory, contentKeys]);

  const tabs = [
    { id: 'tracking', label: t('admin.tracking_analytics'), icon: BarChart3 },
    { id: 'seo', label: t('admin.seo_management'), icon: Globe },
    { id: 'users', label: t('admin.user_management'), icon: Users },
    { id: 'cms', label: t('admin.content_management'), icon: Edit }
  ];

  const pages = [
    { value: 'home', label: t('admin.page_home') },
    { value: 'dashboard', label: t('admin.page_dashboard') },
    { value: 'portfolio', label: t('admin.page_portfolio') },
    { value: 'careers', label: t('admin.page_careers') }
  ];

  const categories = [
    { value: 'all', label: t('admin.all_categories') },
    { value: 'dashboard', label: t('admin.category_dashboard') },
    { value: 'tools', label: t('admin.category_tools') },
    { value: 'portfolio', label: t('admin.category_portfolio') },
    { value: 'careers', label: t('admin.category_careers') },
    { value: 'common', label: t('admin.category_common') }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Settings className="w-8 h-8" />
          {t('admin.settings_title')}
        </h1>
        <p className="text-gray-600">{t('admin.settings_subtitle')}</p>
      </div>

      {message && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {message.text}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'tracking' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              {t('admin.tracking_analytics')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin.tracking_code_label')}
              </label>
              <Textarea
                value={trackingCode}
                onChange={(e) => setTrackingCode(e.target.value)}
                placeholder={t('admin.tracking_code_placeholder')}
                rows={10}
                className="font-mono text-sm"
              />
              <p className="text-sm text-gray-500 mt-2">
                {t('admin.tracking_code_help')}
              </p>
            </div>

            {trackingCode && (
              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('admin.code_preview')}
                </label>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <pre className="text-sm overflow-x-auto">
                    <code>{trackingCode}</code>
                  </pre>
                </div>
                <div className="mt-2">
                  {validateJavaScript(trackingCode) ? (
                    <span className="text-green-600 text-sm">✓ {t('admin.valid_javascript')}</span>
                  ) : (
                    <span className="text-red-600 text-sm">✗ {t('admin.invalid_javascript')}</span>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <Button onClick={saveTrackingCode} disabled={loading}>
                <Save className="w-4 h-4 mr-2" />
                {t('admin.save_tracking')}
              </Button>
              <Button
                variant="outline"
                onClick={() => setTrackingCode('')}
              >
                {t('admin.clear_code')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'seo' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              {t('admin.seo_management')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin.select_page')}
              </label>
              <select
                value={selectedPage}
                onChange={(e) => {
                  setSelectedPage(e.target.value);
                  loadSEOData();
                }}
                className="w-full p-2 border border-gray-300 rounded-lg"
              >
                {pages.map((page) => (
                  <option key={page.value} value={page.value}>
                    {page.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-4">{t('admin.english_seo')}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.page_title')}
                    </label>
                    <Input
                      value={seoData.title_en}
                      onChange={(e) => setSeoData({...seoData, title_en: e.target.value})}
                      placeholder={t('admin.title_placeholder')}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.meta_description')}
                    </label>
                    <Textarea
                      value={seoData.description_en}
                      onChange={(e) => setSeoData({...seoData, description_en: e.target.value})}
                      placeholder={t('admin.description_placeholder')}
                      rows={3}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {seoData.description_en.length}/160 {t('admin.characters')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.keywords')}
                    </label>
                    <Input
                      value={seoData.keywords_en}
                      onChange={(e) => setSeoData({...seoData, keywords_en: e.target.value})}
                      placeholder={t('admin.keywords_placeholder')}
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">{t('admin.ukrainian_seo')}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.page_title')}
                    </label>
                    <Input
                      value={seoData.title_ua}
                      onChange={(e) => setSeoData({...seoData, title_ua: e.target.value})}
                      placeholder={t('admin.title_placeholder')}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.meta_description')}
                    </label>
                    <Textarea
                      value={seoData.description_ua}
                      onChange={(e) => setSeoData({...seoData, description_ua: e.target.value})}
                      placeholder={t('admin.description_placeholder')}
                      rows={3}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {seoData.description_ua.length}/160 {t('admin.characters')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('admin.keywords')}
                    </label>
                    <Input
                      value={seoData.keywords_ua}
                      onChange={(e) => setSeoData({...seoData, keywords_ua: e.target.value})}
                      placeholder={t('admin.keywords_placeholder')}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin.verification_codes')}
              </label>
              <Textarea
                value={seoData.verification_codes}
                onChange={(e) => setSeoData({...seoData, verification_codes: e.target.value})}
                placeholder={t('admin.verification_placeholder')}
                rows={4}
              />
              <p className="text-sm text-gray-500 mt-2">
                {t('admin.verification_help')}
              </p>
            </div>

            <Button onClick={saveSEOData} disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {t('admin.save_seo')}
            </Button>
          </CardContent>
        </Card>
      )}

      {activeTab === 'users' && (
        <div className="space-y-6">
          {/* Create New User */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                {t('admin.create_new_user')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.email')}
                  </label>
                  <Input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    placeholder={t('admin.email_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.password')}
                  </label>
                  <Input
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    placeholder={t('admin.password_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.role')}
                  </label>
                  <select
                    value={newUser.role}
                    onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                  >
                    <option value="user">{t('admin.role_user')}</option>
                    <option value="admin">{t('admin.role_admin')}</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.access_expiry')}
                  </label>
                  <Input
                    type="date"
                    value={newUser.access_expiry_date}
                    onChange={(e) => setNewUser({...newUser, access_expiry_date: e.target.value})}
                  />
                </div>
              </div>
              <div className="mt-4">
                <Button onClick={createUser} disabled={loading}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t('admin.create_user')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                {t('admin.existing_users')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.email')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.role')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.status')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.created_at')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.last_login')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.access_expiry')}</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">{t('admin.actions')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id}>
                        <td className="border border-gray-300 px-4 py-2">{user.email}</td>
                        <td className="border border-gray-300 px-4 py-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role}
                          </span>
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            user.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {user.is_active ? t('admin.active') : t('admin.inactive')}
                          </span>
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : t('admin.never')}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          {user.access_expiry_date ? new Date(user.access_expiry_date).toLocaleDateString() : t('admin.unlimited')}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingUser(user.id)}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateUser(user.id, { is_active: !user.is_active })}
                            >
                              {user.is_active ? t('admin.deactivate') : t('admin.activate')}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'cms' && (
        <div className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                {t('admin.search_filter')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.search_content')}
                  </label>
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={t('admin.search_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('admin.filter_category')}
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                  >
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Keys Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="w-5 h-5" />
                {t('admin.content_translations')} ({filteredContent.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredContent.map((item) => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-sm text-gray-900">{item.key_name}</h4>
                        <p className="text-xs text-gray-500">{item.category} • {item.description}</p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingContent(editingContent === item.id ? null : item.id)}
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        {editingContent === item.id ? t('admin.cancel') : t('admin.edit')}
                      </Button>
                    </div>

                    {editingContent === item.id ? (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              {t('admin.english_translation')}
                            </label>
                            <Textarea
                              value={item.translations.en}
                              onChange={(e) => {
                                const updated = contentKeys.map(k =>
                                  k.id === item.id
                                    ? {...k, translations: {...k.translations, en: e.target.value}}
                                    : k
                                );
                                setContentKeys(updated);
                                filterContent();
                              }}
                              rows={3}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              {t('admin.ukrainian_translation')}
                            </label>
                            <Textarea
                              value={item.translations.ua}
                              onChange={(e) => {
                                const updated = contentKeys.map(k =>
                                  k.id === item.id
                                    ? {...k, translations: {...k.translations, ua: e.target.value}}
                                    : k
                                );
                                setContentKeys(updated);
                                filterContent();
                              }}
                              rows={3}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => updateContent(item.id, item.translations)}
                            disabled={loading}
                          >
                            <Save className="w-3 h-3 mr-1" />
                            {t('admin.save_changes')}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingContent(null)}
                          >
                            {t('admin.cancel')}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">EN:</span> {item.translations.en}
                        </div>
                        <div>
                          <span className="font-medium">UA:</span> {item.translations.ua}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AdminSettings;
