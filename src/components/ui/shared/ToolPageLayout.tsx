import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Info } from 'lucide-react';

interface ToolPageLayoutProps {
  title: string;
  description: string;
  howThisWorksTitle?: string;
  howThisWorksContent?: ReactNode;
  children: ReactNode;
}

const ToolPageLayout: React.FC<ToolPageLayoutProps> = ({
  title,
  description,
  howThisWorksTitle = 'How This Works',
  howThisWorksContent,
  children,
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-gray-800 p-4 sm:p-6 md:p-8 text-gray-100 font-sans">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <Link
            to="/dashboard"
            className="inline-flex items-center text-sky-400 hover:text-sky-300 transition-colors group text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-1.5 transition-transform group-hover:-translate-x-1" />
            Back to Dashboard
          </Link>
        </div>

        <h1 className="text-3xl sm:text-4xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-sky-400 via-cyan-300 to-teal-400 mb-2 sm:mb-3">
          {title}
        </h1>
        <p className="text-base sm:text-lg text-gray-300 mb-6 sm:mb-8 max-w-3xl">
          {description}
        </p>

        {howThisWorksContent && (
          <div className="mb-8 p-5 sm:p-6 bg-slate-800/70 border border-slate-700 rounded-xl shadow-xl">
            <div className="flex items-center text-lg sm:text-xl font-semibold text-sky-300 mb-3">
              <Info className="h-5 w-5 sm:h-6 sm:w-6 mr-2.5 sm:mr-3 text-sky-400 flex-shrink-0" />
              {howThisWorksTitle}
            </div>
            <div className="text-gray-300 text-sm sm:text-base space-y-2">
              {howThisWorksContent}
            </div>
          </div>
        )}

        <main>
          {children}
        </main>
      </div>
    </div>
  );
};

export default ToolPageLayout;
