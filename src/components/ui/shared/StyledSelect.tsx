import React, { SelectHTMLAttributes } from 'react';

interface StyledSelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  themeColor?: 'sky' | 'emerald' | 'amber' | 'purple' | 'slate' | 'red' | 'blue' | 'indigo' | 'pink' | 'gray' | 'orange';
}

const themeFocusRingClasses = {
  sky: 'focus:ring-sky-500',
  emerald: 'focus:ring-emerald-500',
  amber: 'focus:ring-amber-500',
  purple: 'focus:ring-purple-500',
  slate: 'focus:ring-slate-500',
  red: 'focus:ring-red-500',
  blue: 'focus:ring-blue-500',
  indigo: 'focus:ring-indigo-500',
  pink: 'focus:ring-pink-500',
  gray: 'focus:ring-gray-500',
  orange: 'focus:ring-orange-500',
};

const StyledSelect = React.forwardRef<HTMLSelectElement, StyledSelectProps>(
  ({ className, error, themeColor = 'sky', children, ...props }, ref) => {
    const focusRingClass = themeFocusRingClasses[themeColor] || themeFocusRingClasses.sky;
    return (
      <select
        ref={ref}
        className={`
          form-select
          block w-full rounded-md
          bg-slate-700/50 border-slate-600
          text-gray-100 placeholder-gray-400
          shadow-sm transition-colors duration-150 ease-in-out
          focus:border-transparent focus:bg-slate-600/80 focus:outline-none focus:ring-2 ${focusRingClass}
          ${error ? 'border-red-500 focus:ring-red-500' : 'border-slate-600'}
          disabled:opacity-50 disabled:cursor-not-allowed
          px-3 py-2 text-sm
          ${className}
        `}
        {...props}
      >
        {children}
      </select>
    );
  }
);

StyledSelect.displayName = 'StyledSelect';

export default StyledSelect;
