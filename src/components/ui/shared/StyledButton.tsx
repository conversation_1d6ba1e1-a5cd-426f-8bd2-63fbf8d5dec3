import React, { ButtonHTMLAttributes, ReactNode } from 'react';

interface StyledButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'link' | 'icon';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  isLoading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  children?: ReactNode;
  themeColor?: 'sky' | 'emerald' | 'amber' | 'purple' | 'slate' | 'red' | 'blue' | 'indigo' | 'pink' | 'gray' | 'orange';
  tooltipText?: string;
}

const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900 disabled:opacity-50 disabled:pointer-events-none';

const variantClasses: Record<NonNullable<StyledButtonProps['variant']>, string> = {
  primary: 'text-white shadow-sm',
  secondary: 'bg-slate-700 text-slate-100 hover:bg-slate-600 shadow-sm',
  destructive: 'bg-red-600 text-white hover:bg-red-700 shadow-sm',
  outline: 'border border-slate-600 text-slate-100 hover:bg-slate-800 hover:text-slate-50',
  ghost: 'text-slate-100 hover:bg-slate-800 hover:text-slate-50',
  link: 'text-sky-400 underline-offset-4 hover:underline',
  icon: 'text-slate-300 hover:bg-slate-700 hover:text-slate-100',
};

const themeColorClasses: Record<NonNullable<StyledButtonProps['themeColor']>, { primary: string, focusRing: string }> = {
  sky: { primary: 'bg-sky-600 hover:bg-sky-700', focusRing: 'focus-visible:ring-sky-500' },
  emerald: { primary: 'bg-emerald-600 hover:bg-emerald-700', focusRing: 'focus-visible:ring-emerald-500' },
  amber: { primary: 'bg-amber-500 hover:bg-amber-600 text-amber-950', focusRing: 'focus-visible:ring-amber-400' },
  purple: { primary: 'bg-purple-600 hover:bg-purple-700', focusRing: 'focus-visible:ring-purple-500' },
  slate: { primary: 'bg-slate-600 hover:bg-slate-700', focusRing: 'focus-visible:ring-slate-500' }, 
  red: { primary: 'bg-red-600 hover:bg-red-700', focusRing: 'focus-visible:ring-red-500' },
  blue: { primary: 'bg-blue-600 hover:bg-blue-700', focusRing: 'focus-visible:ring-blue-500' },
  indigo: { primary: 'bg-indigo-600 hover:bg-indigo-700', focusRing: 'focus-visible:ring-indigo-500' },
  pink: { primary: 'bg-pink-600 hover:bg-pink-700', focusRing: 'focus-visible:ring-pink-500' },
  gray: { primary: 'bg-gray-600 hover:bg-gray-700', focusRing: 'focus-visible:ring-gray-500' },
  orange: { primary: 'bg-orange-500 hover:bg-orange-600 text-orange-950', focusRing: 'focus-visible:ring-orange-400' },
};

const sizeClasses: Record<NonNullable<StyledButtonProps['size']>, string> = {
  sm: 'px-3 py-1.5 text-xs',
  md: 'px-4 py-2 text-sm',
  lg: 'px-6 py-3 text-base',
  icon: 'p-2',
};

const StyledButton = React.forwardRef<HTMLButtonElement, StyledButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      isLoading = false,
      leftIcon,
      rightIcon,
      children,
      themeColor = 'sky', // Default theme for primary variant
      tooltipText,
      ...props
    },
    ref
  ) => {
    const appliedVariantClasses = variantClasses[variant] || variantClasses.primary;
    const appliedSizeClasses = sizeClasses[size] || sizeClasses.md;
    const currentThemeColors = themeColorClasses[themeColor] || themeColorClasses.sky;

    let finalVariantClasses = appliedVariantClasses;
    if (variant === 'primary') {
      finalVariantClasses = `${currentThemeColors.primary} ${finalVariantClasses}`;
    }

    return (
      <button
        ref={ref}
        className={`
          ${baseClasses}
          ${finalVariantClasses}
          ${currentThemeColors.focusRing}
          ${appliedSizeClasses}
          ${isLoading ? 'cursor-wait' : ''}
          ${className}
        `}
        disabled={isLoading || props.disabled}
        {...props}
        title={tooltipText}
      >
        {isLoading && (
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
        {!isLoading && leftIcon && <span className={children ? 'mr-2' : ''}>{leftIcon}</span>}
        {children}
        {!isLoading && rightIcon && <span className={children ? 'ml-2' : ''}>{rightIcon}</span>}
      </button>
    );
  }
);

StyledButton.displayName = 'StyledButton';

export default StyledButton;
