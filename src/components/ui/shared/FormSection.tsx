import React, { ReactNode } from 'react';

interface FormSectionProps {
  title: string;
  icon: ReactNode; // Expect a Lucide icon component, e.g., <Settings />
  theme: 'sky' | 'emerald' | 'amber' | 'purple' | 'slate' | 'red' | 'blue' | 'indigo' | 'pink' | 'gray' | 'orange';
  children: ReactNode;
  className?: string;
  collapsible?: boolean;
  initiallyCollapsed?: boolean;
}

const themeColorClasses = {
  sky: {
    bg: 'bg-sky-950/70 hover:bg-sky-900/80',
    border: 'border-sky-700/70',
    titleText: 'text-sky-300',
    iconText: 'text-sky-400',
  },
  emerald: {
    bg: 'bg-emerald-950/70 hover:bg-emerald-900/80',
    border: 'border-emerald-700/70',
    titleText: 'text-emerald-300',
    iconText: 'text-emerald-400',
  },
  amber: {
    bg: 'bg-amber-950/70 hover:bg-amber-900/80',
    border: 'border-amber-700/70',
    titleText: 'text-amber-300',
    iconText: 'text-amber-400',
  },
  purple: {
    bg: 'bg-purple-950/70 hover:bg-purple-900/80',
    border: 'border-purple-700/70',
    titleText: 'text-purple-300',
    iconText: 'text-purple-400',
  },
  slate: {
    bg: 'bg-slate-800/70 hover:bg-slate-700/80',
    border: 'border-slate-700/70',
    titleText: 'text-slate-300',
    iconText: 'text-slate-400',
  },
  red: {
    bg: 'bg-red-950/70 hover:bg-red-900/80',
    border: 'border-red-700/70',
    titleText: 'text-red-300',
    iconText: 'text-red-400',
  },
  blue: {
    bg: 'bg-blue-950/70 hover:bg-blue-900/80',
    border: 'border-blue-700/70',
    titleText: 'text-blue-300',
    iconText: 'text-blue-400',
  },
  indigo: {
    bg: 'bg-indigo-950/70 hover:bg-indigo-900/80',
    border: 'border-indigo-700/70',
    titleText: 'text-indigo-300',
    iconText: 'text-indigo-400',
  },
  pink: {
    bg: 'bg-pink-950/70 hover:bg-pink-900/80',
    border: 'border-pink-700/70',
    titleText: 'text-pink-300',
    iconText: 'text-pink-400',
  },
  gray: {
    bg: 'bg-gray-800/70 hover:bg-gray-700/80',
    border: 'border-gray-700/70',
    titleText: 'text-gray-300',
    iconText: 'text-gray-400',
  },
  orange: {
    bg: 'bg-orange-950/70 hover:bg-orange-900/80',
    border: 'border-orange-700/70',
    titleText: 'text-orange-300',
    iconText: 'text-orange-400',
  },
};

const FormSection: React.FC<FormSectionProps> = ({
  title,
  icon,
  theme,
  children,
  className = '',
  // TODO: Implement collapsible functionality if needed later
  // collapsible = false,
  // initiallyCollapsed = false,
}) => {
  const currentTheme = themeColorClasses[theme] || themeColorClasses.slate;

  return (
    <section className={`p-5 sm:p-6 rounded-xl shadow-2xl border transition-colors duration-200 ease-in-out ${currentTheme.bg} ${currentTheme.border} ${className}`}>
      <div className={`flex items-center text-xl sm:text-2xl font-semibold ${currentTheme.titleText} mb-4 sm:mb-6`}>
        {React.isValidElement(icon) ? React.cloneElement(icon, {
          // @ts-expect-error - lucide-react icons don't explicitly type className but accept it
          className: `h-6 w-6 sm:h-7 sm:w-7 mr-2.5 sm:mr-3 ${currentTheme.iconText} flex-shrink-0`,
        }) : null}
        {title}
      </div>
      <div className="space-y-4 sm:space-y-6">
        {children}
      </div>
    </section>
  );
};

export default FormSection;
