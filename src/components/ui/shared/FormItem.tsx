import React, { ReactNode } from 'react';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'; // Assuming these are correctly pathed from ShadCN/ui
import { Info } from 'lucide-react';

interface FormItemProps {
  label: string;
  htmlFor?: string;
  tooltipText?: string;
  children: ReactNode;
  className?: string;
  labelClassName?: string;
  required?: boolean;
}

const FormItem: React.FC<FormItemProps> = ({
  label,
  htmlFor,
  tooltipText,
  children,
  className = '',
  labelClassName = '',
  required = false,
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center">
        <label 
          htmlFor={htmlFor} 
          className={`block text-sm font-medium text-gray-300 ${labelClassName}`}
        >
          {label}{required && <span className="text-red-400 ml-1">*</span>}
        </label>
        {tooltipText && (
          <TooltipProvider delayDuration={200}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button type="button" className="ml-1.5 text-gray-400 hover:text-gray-200">
                  <Info className="h-3.5 w-3.5" />
                </button>
              </TooltipTrigger>
              <TooltipContent side='top' className='max-w-xs bg-slate-800 text-gray-200 border-slate-700 shadow-lg text-xs p-2 rounded-md'>
                <p>{tooltipText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      {children}
    </div>
  );
};

export default FormItem;
