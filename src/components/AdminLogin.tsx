import React, { useState } from 'react';
import Input from './ui/Input';
import Button from './ui/Button';
import { Lock } from 'lucide-react';

interface AdminLoginProps {
  className?: string;
}

const AdminLogin: React.FC<AdminLoginProps> = ({ className }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle login logic here
    console.log('Login with:', username, password);
  };

  return (
    <div className={`bg-white rounded-lg shadow-medium p-8 animate-scale-in ${className}`}>
      <div className="text-center mb-6">
        <div className="mx-auto bg-primary-100 p-3 rounded-full inline-block mb-3">
          <Lock className="h-6 w-6 text-primary-600" />
        </div>
        <h2 className="text-2xl font-bold text-neutral-900">Admin <PERSON>gin</h2>
      </div>
      
      <form onSubmit={handleSubmit}>
        <Input
          label="Username"
          type="text"
          placeholder="Enter your username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
        />
        
        <Input
          label="Password"
          type="password"
          placeholder="Enter your password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        
        <div className="mt-6">
          <Button type="submit" variant="primary" className="w-full font-medium">
            Sign In
          </Button>
        </div>
        
        <div className="mt-4 text-center">
          <a href="#" className="text-sm text-primary-600 hover:text-primary-700 transition-colors">
            Forgot password?
          </a>
        </div>
      </form>
    </div>
  );
};

export default AdminLogin;