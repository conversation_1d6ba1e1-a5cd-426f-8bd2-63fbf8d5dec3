import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import { useSidebar } from '../contexts/SidebarContext';

const DashboardPage: React.FC = () => {
  const { isCollapsed } = useSidebar();

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      <Header />
      <div className="flex flex-1 overflow-hidden pt-16">
        {/* Sidebar - Hidden on mobile, shown on desktop */}
        <aside 
          className={`hidden md:block h-[calc(100vh-64px)] bg-gray-900 border-r border-gray-800 overflow-y-auto z-30 ${
            isCollapsed ? 'w-16' : 'w-64'
          }`}
        >
          <Sidebar />
        </aside>

        {/* Main content - Full width on mobile, adjusted on desktop */}
        <main className={`flex-1 overflow-y-auto w-full`}>
          <div className="p-4 md:p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardPage;
