import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '../components/Header';
import ToolPlaceholder from '../components/tools/ToolPlaceholder';
import TelegramScriptGenerator from '../components/tools/TelegramScriptGenerator';
import SearchQueryPerformance from '../components/tools/SearchQueryPerformance';
import PerformanceMaxAssetAnalyzer from '../components/tools/PerformanceMaxAssetAnalyzer';
import AirtablePnlScriptGenerator from '../components/tools/AirtablePnlScriptGenerator';
import GAdsBudgetUpdaterTool from '../components/tools/GAdsBudgetUpdaterTool';
import CampaignPerformance from '../components/tools/CampaignPerformance';
import AdPerformance from '../components/tools/AdPerformance';
import KeywordPerformance from '../components/tools/KeywordPerformance';
import BudgetMonitor from '../components/tools/BudgetMonitor';
import DeviceBidAdjuster from '../components/tools/DeviceBidAdjuster';
import KeywordConflictDetector from '../components/tools/KeywordConflictDetector';
import ScriptGenerator from '../components/tools/ScriptGenerator';

// Map of tool paths to their corresponding components
const toolComponents: Record<string, React.ComponentType> = {
  'telegram-script-generator': TelegramScriptGenerator,
  'search-query': SearchQueryPerformance,
  'performance-max': PerformanceMaxAssetAnalyzer,
  'airtable-script': AirtablePnlScriptGenerator,
  'gads-budget-updater': GAdsBudgetUpdaterTool,
  'campaign-performance': CampaignPerformance,
  'ad-performance': AdPerformance,
  'keyword-performance': KeywordPerformance,
  'budget-monitor': BudgetMonitor,
  'device-bid': DeviceBidAdjuster,
  'keyword-conflict': KeywordConflictDetector,
  'script-generator': ScriptGenerator,
  // Add more tool components here as they are created
};

const ToolPage: React.FC = () => {
  const location = useLocation();
  const pathSegments = location.pathname.split('/');
  const toolPath = pathSegments[pathSegments.length - 1];
  
  // Get the component for the current tool, or use the placeholder if not found
  const ToolComponent = toolComponents[toolPath] || ToolPlaceholder;

  // Log for debugging
  useEffect(() => {
    console.log('Current tool path:', toolPath);
    console.log('Available tools:', Object.keys(toolComponents));
    console.log('Selected component:', ToolComponent.name);
    console.log('Tool component exists:', !!ToolComponent);
  }, [toolPath, ToolComponent]);

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow bg-gray-900 min-h-[calc(100vh-5rem)] px-4 sm:px-6 lg:px-8 pb-12">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <ToolComponent />
        </div>
      </main>
    </div>
  );
};

export default ToolPage;
