import { useEffect, useState } from 'react';

export interface User {
  login: string;
  password: string;
  role: string;
}

// Function to read and parse the CSV file
export const readCSVFile = async (filePath: string): Promise<User[]> => {
  try {
    const response = await fetch(filePath);
    const csvData = await response.text();
    
    // Split the CSV data into lines
    const lines = csvData.split('\n');
    
    // Skip the first line (header) and parse the rest
    const users: User[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        const [login, password, role] = line.split(';');
        users.push({ login, password, role });
      }
    }
    
    return users;
  } catch (error) {
    console.error('Error reading CSV file:', error);
    return [];
  }
};

// Function to authenticate a user
export const authenticateUser = async (
  email: string, 
  password: string
): Promise<{ success: boolean; user?: User; error?: string }> => {
  try {
    const users = await readCSVFile('/root.csv');
    
    const user = users.find(
      (u) => u.login === email && u.password === password
    );
    
    if (user) {
      return { success: true, user };
    } else {
      return { 
        success: false, 
        error: 'Invalid email or password. Please try again.' 
      };
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return { 
      success: false, 
      error: 'An error occurred during authentication. Please try again later.' 
    };
  }
};

// Custom hook for authentication state
export const useAuth = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Start with loading true to prevent flashing of login screen
  const [loading, setLoading] = useState(true);

  // Check if user is already logged in (from localStorage)
  useEffect(() => {
    const checkAuth = async () => {
      const storedUser = localStorage.getItem('currentUser');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          setCurrentUser(parsedUser);
          setIsAuthenticated(true);
        } catch {
          // Ignore the error details, we just need to handle the case
          // If parsing fails, remove the invalid data
          localStorage.removeItem('currentUser');
        }
      }
      // Set loading to false after checking authentication
      setLoading(false);
    };
    
    // Run the check
    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await authenticateUser(email, password);
      
      if (result.success && result.user) {
        setCurrentUser(result.user);
        setIsAuthenticated(true);
        // Store user in localStorage for persistence
        localStorage.setItem('currentUser', JSON.stringify(result.user));
        return true;
      } else {
        setError(result.error || 'Authentication failed');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('currentUser');
  };

  return {
    currentUser,
    isAuthenticated,
    error,
    loading,
    login,
    logout
  };
};
