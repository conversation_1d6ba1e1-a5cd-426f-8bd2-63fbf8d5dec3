// Analytics configuration
interface AnalyticsConfig {
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  hotjarId?: string;
  hotjarSnippetVersion?: number;
  enabled: boolean;
}

// Global window extensions
declare global {
  interface Window {
    dataLayer: unknown[];
    gtag: (...args: unknown[]) => void;
    fbq: (...args: unknown[]) => void;
    _hjSettings?: {
      hjid: string;
      hjsv: number;
    };
    hj?: (...args: unknown[]) => void;
  }
}

// Default configuration (replace with your actual IDs)
const config: AnalyticsConfig = {
  googleAnalyticsId: process.env.REACT_APP_GA_MEASUREMENT_ID,
  googleTagManagerId: process.env.REACT_APP_GTM_ID,
  facebookPixelId: process.env.REACT_APP_FB_PIXEL_ID,
  hotjarId: process.env.REACT_APP_HOTJAR_ID,
  hotjarSnippetVersion: 6, // Latest Hotjar snippet version
  enabled: process.env.NODE_ENV === 'production',
};

// Initialize Google Analytics
export const initGoogleAnalytics = () => {
  if (!config.enabled || !config.googleAnalyticsId) return;

  // Load Google Analytics script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${config.googleAnalyticsId}`;
  document.head.appendChild(script);

  // Initialize gtag
  window.dataLayer = window.dataLayer || [];
  window.gtag = function(...args: unknown[]) {
    window.dataLayer.push(args);
  };
  
  window.gtag('js', new Date());
  window.gtag('config', config.googleAnalyticsId, {
    page_path: window.location.pathname,
  });
};

// Initialize Google Tag Manager
export const initGoogleTagManager = () => {
  if (!config.enabled || !config.googleTagManagerId) return;

  // GTM script part 1 (in head)
  const gtmScript = document.createElement('script');
  gtmScript.innerHTML = `
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','${config.googleTagManagerId}');
  `;
  document.head.appendChild(gtmScript);

  // GTM noscript part (in body)
  const noscript = document.createElement('noscript');
  const iframe = document.createElement('iframe');
  iframe.src = `https://www.googletagmanager.com/ns.html?id=${config.googleTagManagerId}`;
  iframe.height = '0';
  iframe.width = '0';
  iframe.style.display = 'none';
  iframe.style.visibility = 'hidden';
  noscript.appendChild(iframe);
  document.body.prepend(noscript);
};

// Initialize Facebook Pixel
export const initFacebookPixel = () => {
  if (!config.enabled || !config.facebookPixelId) return;

  // Facebook Pixel script
  const pixelScript = document.createElement('script');
  pixelScript.innerHTML = `
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '${config.facebookPixelId}');
    fbq('track', 'PageView');
  `;
  document.head.appendChild(pixelScript);

  // Add noscript fallback
  const noscript = document.createElement('noscript');
  noscript.innerHTML = `
    <img height="1" width="1" style="display:none" 
         src="https://www.facebook.com/tr?id=${config.facebookPixelId}&ev=PageView&noscript=1"
    />
  `;
  document.body.appendChild(noscript);
};

// Initialize Hotjar
export const initHotjar = () => {
  if (!config.enabled || !config.hotjarId) return;

  const hotjarScript = document.createElement('script');
  hotjarScript.innerHTML = `
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:${config.hotjarId},hjsv:${config.hotjarSnippetVersion}};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  `;
  document.head.appendChild(hotjarScript);
};

// Track page views
export const trackPageView = (path: string, title: string) => {
  if (!config.enabled) return;

  // Google Analytics
  if (typeof window.gtag === 'function') {
    window.gtag('config', config.googleAnalyticsId, {
      page_path: path,
      page_title: title,
    });
  }

  // Facebook Pixel
  if (typeof window.fbq === 'function') {
    window.fbq('track', 'PageView');
  }
};

// Track events
export const trackEvent = (category: string, action: string, label?: string, value?: number) => {
  if (!config.enabled) return;

  // Google Analytics
  if (window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }

  // Facebook Pixel
  if (window.fbq) {
    window.fbq('trackCustom', `${category}_${action}`, {
      label,
      value,
    });
  }
};

// Initialize all analytics
export const initAnalytics = () => {
  if (!config.enabled) return;
  
  try {
    initGoogleAnalytics();
    initGoogleTagManager();
    initFacebookPixel();
    initHotjar();
    
    // Track initial page view
    trackPageView(window.location.pathname, document.title);
  } catch (error) {
    console.error('Error initializing analytics:', error);
  }
};


