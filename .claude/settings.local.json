{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(grep:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker-compose exec:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npm run test-db:*)", "Bash(npm start)", "Bash(node:*)", "<PERSON><PERSON>(echo:*)", "Bash(BCRYPT_LIB=./backend/node_modules/bcrypt PG_LIB=./backend/node_modules/pg node -e \"\nconst bcrypt = require(''./backend/node_modules/bcrypt'');\nconst { Pool } = require(''./backend/node_modules/pg'');\n\nconst pool = new Pool({\n  user: ''postgres'',\n  host: ''localhost'',\n  database: ''gads_db'',\n  password: ''G4d5Str0ng'',\n  port: 5432,\n});\n\nasync function updatePasswords() {\n  try {\n    const adminHash = await bcrypt.hash(''admin123'', 10);\n    const userHash = await bcrypt.hash(''user123'', 10);\n    const demoHash = await bcrypt.hash(''demo123'', 10);\n    \n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [adminHash, ''<EMAIL>'']);\n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [userHash, ''<EMAIL>'']);\n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [demoHash, ''<EMAIL>'']);\n    \n    console.log(''✅ Passwords updated successfully!'');\n    await pool.end();\n    process.exit(0);\n  } catch (error) {\n    console.error(''❌ Error:'', error);\n    process.exit(1);\n  }\n}\n\nupdatePasswords();\n\")"], "deny": []}}