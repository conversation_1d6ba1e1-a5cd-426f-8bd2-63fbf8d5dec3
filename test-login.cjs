const http = require('http');

const postData = JSON.stringify({
  email: '<EMAIL>',
  password: 'Admin2025!Secure#'
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🔐 Testing authentication...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
    if (res.statusCode === 200) {
      console.log('✅ Authentication successful!');
      try {
        const jsonData = JSON.parse(data);
        console.log('Token received:', jsonData.token ? 'Yes' : 'No');
      } catch (e) {
        console.log('Failed to parse JSON response');
      }
    } else {
      console.log('❌ Authentication failed');
    }
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.write(postData);
req.end();
