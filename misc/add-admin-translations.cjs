const { Pool } = require('pg');

const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

const translations = [
  // Admin Settings
  { key: 'admin.access_denied', en: 'Access Denied', ua: 'Доступ Заборонено' },
  { key: 'admin.admin_only', en: 'This page is only accessible to administrators.', ua: 'Ця сторінка доступна тільки адміністраторам.' },
  { key: 'admin.settings_title', en: 'Admin Settings', ua: 'Налаштування Адміністратора' },
  { key: 'admin.settings_subtitle', en: 'Manage tracking, SEO, users, and content translations', ua: 'Управління відстеженням, SEO, користувачами та перекладами контенту' },
  { key: 'admin.tracking_analytics', en: 'Tracking & Analytics', ua: 'Відстеження та Аналітика' },
  { key: 'admin.seo_management', en: 'SEO Management', ua: 'Управління SEO' },
  { key: 'admin.user_management', en: 'User Management', ua: 'Управління Користувачами' },
  { key: 'admin.content_management', en: 'Content Management', ua: 'Управління Контентом' },
  { key: 'admin.load_error', en: 'Error loading data', ua: 'Помилка завантаження даних' },
  { key: 'admin.save_error', en: 'Error saving data', ua: 'Помилка збереження даних' },
  { key: 'admin.tracking_code_label', en: 'JavaScript Tracking Code', ua: 'JavaScript Код Відстеження' },
  { key: 'admin.tracking_code_placeholder', en: 'Insert Google Tag Manager, Facebook Pixel, or other tracking codes here...', ua: 'Вставте Google Tag Manager, Facebook Pixel або інші коди відстеження тут...' },
  { key: 'admin.tracking_code_help', en: 'This code will be injected into the <head> section of all pages.', ua: 'Цей код буде вставлений в секцію <head> всіх сторінок.' },
  { key: 'admin.code_preview', en: 'Code Preview', ua: 'Попередній Перегляд Коду' },
  { key: 'admin.valid_javascript', en: 'Valid JavaScript syntax', ua: 'Валідний синтаксис JavaScript' },
  { key: 'admin.invalid_javascript', en: 'Invalid JavaScript syntax', ua: 'Невалідний синтаксис JavaScript' },
  { key: 'admin.save_tracking', en: 'Save Tracking Code', ua: 'Зберегти Код Відстеження' },
  { key: 'admin.clear_code', en: 'Clear Code', ua: 'Очистити Код' },
  { key: 'admin.tracking_saved', en: 'Tracking code saved successfully', ua: 'Код відстеження успішно збережено' },
  { key: 'admin.select_page', en: 'Select Page', ua: 'Оберіть Сторінку' },
  { key: 'admin.page_home', en: 'Home Page', ua: 'Головна Сторінка' },
  { key: 'admin.page_dashboard', en: 'Dashboard', ua: 'Панель Управління' },
  { key: 'admin.page_portfolio', en: 'Portfolio', ua: 'Портфоліо' },
  { key: 'admin.page_careers', en: 'Careers', ua: 'Карєра' },
  { key: 'admin.english_seo', en: 'English SEO', ua: 'Англійське SEO' },
  { key: 'admin.ukrainian_seo', en: 'Ukrainian SEO', ua: 'Українське SEO' },
  { key: 'admin.page_title', en: 'Page Title', ua: 'Заголовок Сторінки' },
  { key: 'admin.title_placeholder', en: 'Enter page title...', ua: 'Введіть заголовок сторінки...' },
  { key: 'admin.meta_description', en: 'Meta Description', ua: 'Мета Опис' },
  { key: 'admin.description_placeholder', en: 'Enter meta description...', ua: 'Введіть мета опис...' },
  { key: 'admin.description_too_long', en: 'Meta description must be 160 characters or less', ua: 'Мета опис повинен бути не більше 160 символів' },
  { key: 'admin.keywords', en: 'Keywords', ua: 'Ключові Слова' },
  { key: 'admin.keywords_placeholder', en: 'keyword1, keyword2, keyword3', ua: 'ключове_слово1, ключове_слово2, ключове_слово3' },
  { key: 'admin.characters', en: 'characters', ua: 'символів' },
  { key: 'admin.verification_codes', en: 'Domain Verification Codes', ua: 'Коди Верифікації Домену' },
  { key: 'admin.verification_placeholder', en: 'Google Search Console, Bing Webmaster Tools verification codes...', ua: 'Google Search Console, Bing Webmaster Tools коди верифікації...' },
  { key: 'admin.verification_help', en: 'Enter verification meta tags for search engines and webmaster tools.', ua: 'Введіть мета теги верифікації для пошукових систем та вебмастер інструментів.' },
  { key: 'admin.save_seo', en: 'Save SEO Settings', ua: 'Зберегти SEO Налаштування' },
  { key: 'admin.seo_saved', en: 'SEO settings saved successfully', ua: 'SEO налаштування успішно збережено' },
  { key: 'admin.create_new_user', en: 'Create New User', ua: 'Створити Нового Користувача' },
  { key: 'admin.email', en: 'Email', ua: 'Електронна Пошта' },
  { key: 'admin.email_placeholder', en: '<EMAIL>', ua: '<EMAIL>' },
  { key: 'admin.password', en: 'Password', ua: 'Пароль' },
  { key: 'admin.password_placeholder', en: 'Enter password...', ua: 'Введіть пароль...' },
  { key: 'admin.role', en: 'Role', ua: 'Роль' },
  { key: 'admin.role_user', en: 'User', ua: 'Користувач' },
  { key: 'admin.role_admin', en: 'Admin', ua: 'Адміністратор' },
  { key: 'admin.access_expiry', en: 'Access Expiry Date', ua: 'Дата Закінчення Доступу' },
  { key: 'admin.create_user', en: 'Create User', ua: 'Створити Користувача' },
  { key: 'admin.fill_required_fields', en: 'Please fill in all required fields', ua: 'Будь ласка, заповніть всі обовязкові поля' },
  { key: 'admin.user_created', en: 'User created successfully', ua: 'Користувача успішно створено' },
  { key: 'admin.create_user_error', en: 'Error creating user', ua: 'Помилка створення користувача' },
  { key: 'admin.existing_users', en: 'Existing Users', ua: 'Існуючі Користувачі' },
  { key: 'admin.status', en: 'Status', ua: 'Статус' },
  { key: 'admin.created_at', en: 'Created At', ua: 'Створено' },
  { key: 'admin.last_login', en: 'Last Login', ua: 'Останній Вхід' },
  { key: 'admin.actions', en: 'Actions', ua: 'Дії' },
  { key: 'admin.active', en: 'Active', ua: 'Активний' },
  { key: 'admin.inactive', en: 'Inactive', ua: 'Неактивний' },
  { key: 'admin.never', en: 'Never', ua: 'Ніколи' },
  { key: 'admin.unlimited', en: 'Unlimited', ua: 'Необмежено' },
  { key: 'admin.activate', en: 'Activate', ua: 'Активувати' },
  { key: 'admin.deactivate', en: 'Deactivate', ua: 'Деактивувати' },
  { key: 'admin.edit', en: 'Edit', ua: 'Редагувати' },
  { key: 'admin.user_updated', en: 'User updated successfully', ua: 'Користувача успішно оновлено' },
  { key: 'admin.update_user_error', en: 'Error updating user', ua: 'Помилка оновлення користувача' },
  { key: 'admin.search_filter', en: 'Search & Filter', ua: 'Пошук та Фільтр' },
  { key: 'admin.search_content', en: 'Search Content', ua: 'Пошук Контенту' },
  { key: 'admin.search_placeholder', en: 'Search by key name or translation...', ua: 'Пошук за назвою ключа або перекладом...' },
  { key: 'admin.filter_category', en: 'Filter by Category', ua: 'Фільтр за Категорією' },
  { key: 'admin.all_categories', en: 'All Categories', ua: 'Всі Категорії' },
  { key: 'admin.category_dashboard', en: 'Dashboard', ua: 'Панель Управління' },
  { key: 'admin.category_tools', en: 'Tools', ua: 'Інструменти' },
  { key: 'admin.category_portfolio', en: 'Portfolio', ua: 'Портфоліо' },
  { key: 'admin.category_careers', en: 'Careers', ua: 'Карєра' },
  { key: 'admin.category_common', en: 'Common', ua: 'Загальні' },
  { key: 'admin.content_translations', en: 'Content Translations', ua: 'Переклади Контенту' },
  { key: 'admin.english_translation', en: 'English Translation', ua: 'Англійський Переклад' },
  { key: 'admin.ukrainian_translation', en: 'Ukrainian Translation', ua: 'Український Переклад' },
  { key: 'admin.save_changes', en: 'Save Changes', ua: 'Зберегти Зміни' },
  { key: 'admin.cancel', en: 'Cancel', ua: 'Скасувати' },
  { key: 'admin.content_updated', en: 'Content updated successfully', ua: 'Контент успішно оновлено' },
  { key: 'admin.update_content_error', en: 'Error updating content', ua: 'Помилка оновлення контенту' }
];

async function addTranslations() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Adding admin translations...');
    
    for (const translation of translations) {
      // First, ensure the content key exists
      const keyResult = await client.query(`
        INSERT INTO content_keys (key_name, category, description, created_at, updated_at) 
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT (key_name) DO UPDATE SET updated_at = NOW()
        RETURNING id
      `, [translation.key, 'admin', `Admin translation for ${translation.key}`]);
      
      let keyId;
      if (keyResult.rows.length > 0) {
        keyId = keyResult.rows[0].id;
      } else {
        // Get existing key ID
        const existingKey = await client.query('SELECT id FROM content_keys WHERE key_name = $1', [translation.key]);
        keyId = existingKey.rows[0].id;
      }
      
      // Add English translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at) 
        VALUES ($1, 'en', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET 
        translation_text = EXCLUDED.translation_text, updated_at = NOW()
      `, [keyId, translation.en]);
      
      // Add Ukrainian translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at) 
        VALUES ($1, 'ua', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET 
        translation_text = EXCLUDED.translation_text, updated_at = NOW()
      `, [keyId, translation.ua]);
      
      console.log(`✅ Added: ${translation.key}`);
    }
    
    console.log('🎉 All admin translations added!');
    
  } catch (error) {
    console.error('❌ Error adding translations:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addTranslations();
