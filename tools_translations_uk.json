{"tools.budgetUpdater.title": "Оновлювач бюджету Google Ads", "tools.budgetUpdater.description": "Автоматично збільшує бюджети кампаній до вказаного максимуму з опціональними сповіщеннями в Telegram.", "tools.budgetUpdater.sections.campaign": "Налаштування кампанії", "tools.budgetUpdater.sections.budget": "Параметри бюджету", "tools.budgetUpdater.sections.notifications": "Налаштування сповіщень (Опціонально)", "tools.budgetUpdater.fields.campaignPattern": "Шаблон назви кампанії", "tools.budgetUpdater.fields.campaignType": "Тип кампанії (Опціонально)", "tools.budgetUpdater.fields.maxBudget": "Максимальний бюджет", "tools.budgetUpdater.fields.minBudget": "Мінімальний бюджет", "tools.budgetUpdater.fields.maxIncrements": "Макс. збільшень", "tools.budgetUpdater.fields.incrementPercentage": "Відсоток збільшення", "tools.budgetUpdater.fields.maxBudgetIncrease": "Макс. збільшення бюджету", "tools.budgetUpdater.fields.telegramToken": "Токен Telegram бота", "tools.budgetUpdater.fields.telegramChatId": "ID чату Telegram", "tools.budgetUpdater.tooltips.campaignPattern": "Введіть точну назву або шаблон кампанії(й). Враховується регістр.", "tools.budgetUpdater.tooltips.campaignType": "Вкажіть тип кампанії, якщо хочете переконатися, що скрипт націлений на конкретний тип (наприклад, PERFORMANCE_MAX). Залиште порожнім для пошуку серед усіх типів.", "tools.budgetUpdater.tooltips.maxBudget": "Абсолютний максимальний бюджет, який кампанія не повинна перевищувати.", "tools.budgetUpdater.tooltips.minBudget": "Мінімальний бюджет, нижче якого кампанія не повинна опускатися.", "tools.budgetUpdater.tooltips.maxIncrements": "Макси<PERSON>а<PERSON><PERSON>на кількість разів, коли бюджет може бути збільшений.", "tools.budgetUpdater.tooltips.incrementPercentage": "Відсоток (наприклад, 0.1 для 10%) на який збільшувати бюджет. Застосовується до поточного бюджету.", "tools.budgetUpdater.tooltips.maxBudgetIncrease": "Максимальний відсоток, на який може бути збільшений бюджет.", "tools.budgetUpdater.tooltips.telegramToken": "Ваш токен Telegram бота для сповіщень. Зберігається локально.", "tools.budgetUpdater.tooltips.telegramChatId": "Ваш ID чату Telegram для сповіщень. Зберігається локально.", "tools.budgetUpdater.campaignTypes.all": "Всі типи (Рекомендовано, якщо не впевнені)", "tools.budgetUpdater.campaignTypes.search": "По<PERSON><PERSON>к", "tools.budgetUpdater.campaignTypes.shopping": "Торгівля", "tools.budgetUpdater.campaignTypes.display": "Медійна", "tools.budgetUpdater.campaignTypes.video": "Відео", "tools.budgetUpdater.campaignTypes.app": "Додаток", "tools.budgetUpdater.campaignTypes.smart": "Розумна", "tools.budgetUpdater.campaignTypes.hotel": "Готель", "tools.budgetUpdater.generated": "Згенерований скрипт оновлення бюджету", "tools.budgetMonitor.title": "Монітор темпу бюджету Google Ads", "tools.budgetMonitor.description": "Відстежує темп щоденних витрат вказаних кампаній Google Ads і надсилає сповіщення, якщо вони значно перевищують або не досягають бюджету. Допомагає запобігти перевитратам і забезпечує ефективне використання виділеного бюджету.", "tools.budgetMonitor.howItWorks": "Як це працює", "tools.budgetMonitor.sections.campaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON> кампан<PERSON>й", "tools.budgetMonitor.sections.thresholds": "Пороги темпу (% від очікуваних витрат)", "tools.budgetMonitor.sections.notifications": "Сповіщення Telegram", "tools.budgetMonitor.fields.campaignNames": "Назви кампаній для моніторингу", "tools.budgetMonitor.fields.highThreshold": "Високий поріг темпу (%) - Сповіщення при перевищенні", "tools.budgetMonitor.fields.lowThreshold": "Низький поріг темпу (%) - Сповіщення при недостачі", "tools.budgetMonitor.fields.telegramToken": "Токен Telegram бота", "tools.budgetMonitor.fields.telegramChatId": "ID чату Telegram", "tools.budgetMonitor.tooltips.campaignNames": "Введіть одну точну назву кампанії на рядок. Скрипт буде відстежувати кожну з цих кампаній.", "tools.budgetMonitor.tooltips.highThreshold": "Сповіщення, якщо поточні витрати БІЛЬШЕ цього відсотка від очікуваних витрат на час дня. Наприклад, 120 означає сповіщення, якщо темп на 20% вищий.", "tools.budgetMonitor.tooltips.lowThreshold": "Сповіщення, якщо поточні витрати МЕНШЕ цього відсотка від очікуваних витрат. Наприклад, 80 означає сповіщення, якщо темп на 20% нижчий.", "tools.budgetMonitor.tooltips.telegramToken": "Ваш токен Telegram бота.", "tools.budgetMonitor.tooltips.telegramChatId": "Ваш ID чату Telegram.", "tools.budgetMonitor.generated": "Згенерований скрипт", "tools.performanceMax.title": "Аналіза<PERSON><PERSON><PERSON> ресурсів Performance Max", "tools.performanceMax.description": "Генерує скрипт Google Ads для виявлення низькоефективних ресурсів (текст, зображення, відео) у кампаніях Performance Max на основі їх міток ефективності (наприклад, 'LOW'). Сповіщає електронною поштою та опціонально через Telegram.", "tools.performanceMax.sections.campaign": "Налаштування кампанії та дат", "tools.performanceMax.sections.notifications": "Налаштування сповіщень", "tools.performanceMax.sections.telegram": "Деталі Telegram", "tools.performanceMax.fields.campaignPattern": "Шаблон назви кампанії (містить, без урахування регістру)", "tools.performanceMax.fields.dateRange": "Діапазон дат", "tools.performanceMax.fields.email": "Електронна адреса для сповіщень", "tools.performanceMax.fields.enableTelegram": "Увімкнути сповіщення Telegram", "tools.performanceMax.fields.telegramToken": "Токен Telegram бота", "tools.performanceMax.fields.telegramChatId": "ID чату Telegram", "tools.performanceMax.tooltips.campaignPattern": "Введіть частину назви вашої PMax кампанії, наприклад, 'Brand_PMax_USA'. Скрипт шукатиме кампанії, що містять цей текст.", "tools.performanceMax.tooltips.dateRange": "Виберіть період для аналізу ефективності ресурсів.", "tools.performanceMax.tooltips.email": "Скрипт надішле звіт на цю електронну адресу.", "tools.performanceMax.tooltips.telegramToken": "API токен вашого Telegram бота.", "tools.performanceMax.tooltips.telegramChatId": "ID чату для надсилання сповіщень (ID користувача, групи або каналу).", "tools.performanceMax.placeholders.campaignPattern": "наприклад, My_PMax_Campaign", "tools.performanceMax.placeholders.telegramToken": "Введіть токен бота", "tools.deviceBid.title": "Генера<PERSON>ор скрипта коригування ставок пристроїв Google Ads", "tools.deviceBid.description": "Цей інструмент генерує скрипт Google Ads для автоматичного коригування модифікаторів ставок пристроїв (Моб<PERSON><PERSON><PERSON>ний, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, План<PERSON>е<PERSON>) на основі їх ефективності Cost-Per-Click (CPC) відносно ваших визначених порогів. Допомагає оптимізувати ставки для покращення ефективності кампанії.", "tools.deviceBid.howItWorks": "Як це працює", "tools.deviceBid.sections.targeting": "Тарге<PERSON><PERSON><PERSON><PERSON> кампаній та діапазон дат", "tools.deviceBid.sections.thresholds": "Пороги CPC пристроїв та коригування", "tools.deviceBid.sections.notifications": "Сповіщення Telegram", "tools.deviceBid.fields.campaignPattern": "Шаблон назви кампанії (Опціонально)", "tools.deviceBid.fields.dateRange": "Виберіть діапазон дат", "tools.deviceBid.fields.mobileThreshold": "Поріг CPC мобільних", "tools.deviceBid.fields.desktopThreshold": "Поріг CPC десктопу", "tools.deviceBid.fields.tabletThreshold": "Поріг CPC планшету", "tools.deviceBid.fields.adjustmentPercentage": "Відсоток коригування (%)", "tools.deviceBid.fields.telegramToken": "Токен Telegram бота", "tools.deviceBid.fields.telegramChatId": "ID чату Telegram", "tools.deviceBid.tooltips.campaignPattern": "Введіть частину назви кампанії для націлювання на конкретні кампанії (наприклад, 'Brand'). Залиште порожнім для розгляду всіх пошукових кампаній.", "tools.deviceBid.tooltips.dateRange": "Період, за який будуть аналізуватися дані ефективності пристроїв.", "tools.deviceBid.tooltips.mobileThreshold": "Якщо CPC мобільних перевищує це значення, ставка буде зменшена.", "tools.deviceBid.tooltips.desktopThreshold": "Якщо CPC десктопу перевищує це значення, ставка буде зменшена.", "tools.deviceBid.tooltips.tabletThreshold": "Якщо CPC планшету перевищує це значення, ставка буде зменшена.", "tools.deviceBid.tooltips.adjustmentPercentage": "Відсоток для збільшення/зменшення ставок (1-90). Наприклад, введіть 10 для 10%.", "tools.deviceBid.tooltips.telegramToken": "Ваш токен Telegram бота.", "tools.deviceBid.tooltips.telegramChatId": "Ваш ID чату Telegram.", "tools.deviceBid.placeholders.mobileThreshold": "наприклад, 1.50", "tools.deviceBid.placeholders.desktopThreshold": "наприклад, 2.00", "tools.deviceBid.placeholders.tabletThreshold": "наприклад, 1.80", "tools.deviceBid.generated": "Згенерований скрипт"}