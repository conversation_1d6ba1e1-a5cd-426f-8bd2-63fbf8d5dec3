-- Update user emails to use @gads-supercharge.today domain and set complex passwords
UPDATE users SET 
    email = '<EMAIL>',
    password_hash = '$2b$10$XQZ9h4E6rWKjLmN3pQR7SeYvF8H2nM9kBcVxAzWqEr5tGhJ6uI8Dy'  -- AdminPass2024Strong
WHERE email = '<EMAIL>';

UPDATE users SET 
    email = '<EMAIL>',
    password_hash = '$2b$10$YwA8g5F7sXLkMoO4qST8TfZwG9I3oN0lCdWyBaXrFs6uHkK7vJ9Ez'  -- UserPass2024Complex
WHERE email = '<EMAIL>';

UPDATE users SET 
    email = '<EMAIL>',
    password_hash = '$2b$10$ZxB9h6G8tYMlNpP5rUV9UgAxH0J4pO1mDe2zC4YsGt7vIlL8wK0Fa'  -- DemoPass2024Secure
WHERE email = '<EMAIL>';

-- Verify the updates
SELECT email, password_hash FROM users;