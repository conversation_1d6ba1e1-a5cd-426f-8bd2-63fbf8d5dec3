-- Add only the missing translation keys that don't exist yet
INSERT INTO content_keys (key_name, category, description) 
SELECT * FROM (VALUES
    ('tools.scriptGenerator.howItWorks.step1', 'tools', 'Script Generator how it works step 1'),
    ('tools.scriptGenerator.howItWorks.step2', 'tools', 'Script Generator how it works step 2'), 
    ('tools.scriptGenerator.howItWorks.step3', 'tools', 'Script Generator how it works step 3'),
    ('tools.scriptGenerator.howItWorks.step4', 'tools', 'Script Generator how it works step 4'),
    ('tools.scriptGenerator.howItWorks.step5', 'tools', 'Script Generator how it works step 5'),
    ('tools.scriptGenerator.sections.campaignDetails', 'tools', 'Campaign details section'),
    ('tools.scriptGenerator.sections.adContent', 'tools', 'Ad content section'),
    ('tools.scriptGenerator.sections.keywords', 'tools', 'Keywords section'),
    ('tools.scriptGenerator.sections.telegram', 'tools', 'Telegram notifications section'),
    ('tools.scriptGenerator.fields.finalUrl', 'tools', 'Final URL field'),
    ('tools.scriptGenerator.fields.campaignName', 'tools', 'Campaign name field'),
    ('tools.scriptGenerator.fields.adGroupName', 'tools', 'Ad group name field'),
    ('tools.scriptGenerator.fields.dailyBudget', 'tools', 'Daily budget field'),
    ('tools.scriptGenerator.fields.headlines', 'tools', 'Headlines field'),
    ('tools.scriptGenerator.fields.descriptions', 'tools', 'Descriptions field'),
    ('tools.scriptGenerator.fields.keyword', 'tools', 'Keyword field'),
    ('tools.scriptGenerator.fields.enableTelegram', 'tools', 'Enable Telegram field'),
    ('tools.scriptGenerator.fields.telegramBotToken', 'tools', 'Telegram bot token field'),
    ('tools.scriptGenerator.fields.telegramChatId', 'tools', 'Telegram chat ID field'),
    ('tools.scriptGenerator.placeholders.headline', 'tools', 'Headline placeholder'),
    ('tools.scriptGenerator.placeholders.description', 'tools', 'Description placeholder'),
    ('tools.scriptGenerator.placeholders.keyword', 'tools', 'Keyword placeholder'),
    ('tools.scriptGenerator.tooltips.finalUrl', 'tools', 'Final URL tooltip'),
    ('tools.scriptGenerator.tooltips.campaignName', 'tools', 'Campaign name tooltip'),
    ('tools.scriptGenerator.tooltips.adGroupName', 'tools', 'Ad group name tooltip'),
    ('tools.scriptGenerator.tooltips.dailyBudget', 'tools', 'Daily budget tooltip'),
    ('tools.scriptGenerator.tooltips.removeHeadline', 'tools', 'Remove headline tooltip'),
    ('tools.scriptGenerator.tooltips.removeDescription', 'tools', 'Remove description tooltip'),
    ('tools.scriptGenerator.tooltips.removeKeyword', 'tools', 'Remove keyword tooltip'),
    ('tools.scriptGenerator.tooltips.telegramBotToken', 'tools', 'Telegram bot token tooltip'),
    ('tools.scriptGenerator.tooltips.telegramChatId', 'tools', 'Telegram chat ID tooltip'),
    ('tools.scriptGenerator.buttons.addHeadline', 'tools', 'Add headline button'),
    ('tools.scriptGenerator.buttons.addDescription', 'tools', 'Add description button'),
    ('tools.scriptGenerator.buttons.addKeyword', 'tools', 'Add keyword button'),
    ('tools.scriptGenerator.buttons.generateScript', 'tools', 'Generate script button'),
    ('tools.scriptGenerator.results.title', 'tools', 'Generated script title'),
    ('tools.scriptGenerator.errors.requiredFields', 'tools', 'Required fields error'),
    ('tools.scriptGenerator.errors.invalidBudget', 'tools', 'Invalid budget error'),
    ('tools.scriptGenerator.errors.invalidUrl', 'tools', 'Invalid URL error'),
    ('tools.scriptGenerator.errors.telegramRequired', 'tools', 'Telegram required error'),
    ('tools.scriptGenerator.errors.noAds', 'tools', 'No ads error'),
    ('tools.scriptGenerator.errors.incompleteAds', 'tools', 'Incomplete ads error'),
    ('tools.scriptGenerator.errors.noKeywords', 'tools', 'No keywords error'),
    ('tools.scriptGenerator.success.generated', 'tools', 'Script generated success'),
    ('common.backToForm', 'common', 'Back to form button'),
    ('common.generateScript', 'common', 'Generate script button'),
    ('common.enableTelegram', 'common', 'Enable Telegram notifications'),
    ('common.generating', 'common', 'Generating text'),
    ('common.scriptGenerated', 'common', 'Script generated successfully'),
    ('dashboard.openTool', 'dashboard', 'Open tool button'),
    ('dashboard.toolsSection', 'dashboard', 'Tools section'),
    ('dashboard.recentAdjustments', 'dashboard', 'Recent adjustments section'),
    ('auth.role', 'auth', 'Role label'),
    ('auth.status', 'auth', 'Status label'),
    ('auth.active', 'auth', 'Active status')
) AS v(key_name, category, description)
WHERE NOT EXISTS (
    SELECT 1 FROM content_keys ck WHERE ck.key_name = v.key_name
);

-- Add English translations
DO $$
DECLARE
    r RECORD;
    key_id UUID;
BEGIN
    FOR r IN 
        SELECT key_name FROM content_keys 
        WHERE key_name LIKE 'tools.scriptGenerator%' 
           OR key_name IN ('common.backToForm', 'common.generateScript', 'common.enableTelegram', 'common.generating', 'common.scriptGenerated', 'dashboard.openTool', 'dashboard.toolsSection', 'dashboard.recentAdjustments', 'auth.role', 'auth.status', 'auth.active')
    LOOP
        SELECT id INTO key_id FROM content_keys WHERE key_name = r.key_name;
        
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        SELECT key_id, 'en', 
        CASE r.key_name
            WHEN 'tools.scriptGenerator.howItWorks.step1' THEN 'Fill in your campaign details, target URL, and budget.'
            WHEN 'tools.scriptGenerator.howItWorks.step2' THEN 'Craft compelling ads by adding multiple headlines and descriptions.'
            WHEN 'tools.scriptGenerator.howItWorks.step3' THEN 'List relevant keywords that will trigger your ads.'
            WHEN 'tools.scriptGenerator.howItWorks.step4' THEN 'Optionally, enable Telegram notifications for script execution updates.'
            WHEN 'tools.scriptGenerator.howItWorks.step5' THEN 'Click "Generate Script" to get a ready-to-use Google Ads script.'
            WHEN 'tools.scriptGenerator.sections.campaignDetails' THEN 'Campaign & Ad Group Details'
            WHEN 'tools.scriptGenerator.sections.adContent' THEN 'Ad Content'
            WHEN 'tools.scriptGenerator.sections.keywords' THEN 'Keywords'
            WHEN 'tools.scriptGenerator.sections.telegram' THEN 'Telegram Notifications (Optional)'
            WHEN 'tools.scriptGenerator.fields.finalUrl' THEN 'Final URL'
            WHEN 'tools.scriptGenerator.fields.campaignName' THEN 'Campaign Name'
            WHEN 'tools.scriptGenerator.fields.adGroupName' THEN 'Ad Group Name'
            WHEN 'tools.scriptGenerator.fields.dailyBudget' THEN 'Daily Budget'
            WHEN 'tools.scriptGenerator.fields.headlines' THEN 'Headlines (up to 15)'
            WHEN 'tools.scriptGenerator.fields.descriptions' THEN 'Descriptions (up to 4)'
            WHEN 'tools.scriptGenerator.fields.keyword' THEN 'Keyword'
            WHEN 'tools.scriptGenerator.fields.enableTelegram' THEN 'Enable Telegram Notifications'
            WHEN 'tools.scriptGenerator.fields.telegramBotToken' THEN 'Telegram Bot Token'
            WHEN 'tools.scriptGenerator.fields.telegramChatId' THEN 'Telegram Chat ID'
            WHEN 'tools.scriptGenerator.placeholders.headline' THEN 'Headline'
            WHEN 'tools.scriptGenerator.placeholders.description' THEN 'Description'
            WHEN 'tools.scriptGenerator.placeholders.keyword' THEN 'e.g., buy online widgets'
            WHEN 'tools.scriptGenerator.tooltips.finalUrl' THEN 'The landing page URL for your ads.'
            WHEN 'tools.scriptGenerator.tooltips.campaignName' THEN 'Name for your new campaign.'
            WHEN 'tools.scriptGenerator.tooltips.adGroupName' THEN 'Name for the primary ad group in this campaign.'
            WHEN 'tools.scriptGenerator.tooltips.dailyBudget' THEN 'Daily budget for the campaign (e.g., 50). Set to 0 for no budget (uses shared budget).'
            WHEN 'tools.scriptGenerator.tooltips.removeHeadline' THEN 'Remove Headline'
            WHEN 'tools.scriptGenerator.tooltips.removeDescription' THEN 'Remove Description'
            WHEN 'tools.scriptGenerator.tooltips.removeKeyword' THEN 'Remove Keyword'
            WHEN 'tools.scriptGenerator.tooltips.telegramBotToken' THEN 'Your Telegram Bot API Token.'
            WHEN 'tools.scriptGenerator.tooltips.telegramChatId' THEN 'Your Telegram User/Group Chat ID.'
            WHEN 'tools.scriptGenerator.buttons.addHeadline' THEN 'Add Headline'
            WHEN 'tools.scriptGenerator.buttons.addDescription' THEN 'Add Description'
            WHEN 'tools.scriptGenerator.buttons.addKeyword' THEN 'Add New Keyword'
            WHEN 'tools.scriptGenerator.buttons.generateScript' THEN 'Generate Script'
            WHEN 'tools.scriptGenerator.results.title' THEN 'Generated Google Ads Script'
            WHEN 'tools.scriptGenerator.errors.requiredFields' THEN 'Campaign Name, Ad Group Name, and Final URL are required.'
            WHEN 'tools.scriptGenerator.errors.invalidBudget' THEN 'Budget must be a positive number.'
            WHEN 'tools.scriptGenerator.errors.invalidUrl' THEN 'Please enter a valid Final URL (e.g., https://example.com).'
            WHEN 'tools.scriptGenerator.errors.telegramRequired' THEN 'Telegram Bot Token and Chat ID are required when Telegram notifications are enabled.'
            WHEN 'tools.scriptGenerator.errors.noAds' THEN 'Please add at least one ad with headlines and descriptions.'
            WHEN 'tools.scriptGenerator.errors.incompleteAds' THEN 'Please ensure all active ads have at least one headline and one description.'
            WHEN 'tools.scriptGenerator.errors.noKeywords' THEN 'Please add at least one keyword.'
            WHEN 'tools.scriptGenerator.success.generated' THEN 'Script generated successfully!'
            WHEN 'common.backToForm' THEN 'Back to Form'
            WHEN 'common.generateScript' THEN 'Generate Script'
            WHEN 'common.enableTelegram' THEN 'Enable Telegram Notifications'
            WHEN 'common.generating' THEN 'Generating...'
            WHEN 'common.scriptGenerated' THEN 'Script generated successfully!'
            WHEN 'dashboard.openTool' THEN 'Open Tool'
            WHEN 'dashboard.toolsSection' THEN 'Tools'
            WHEN 'dashboard.recentAdjustments' THEN 'Recent Budget Adjustments'
            WHEN 'auth.role' THEN 'Role'
            WHEN 'auth.status' THEN 'Status'
            WHEN 'auth.active' THEN 'Active'
            ELSE 'Translation needed'
        END
        WHERE NOT EXISTS (
            SELECT 1 FROM content_translations ct 
            WHERE ct.content_key_id = key_id AND ct.language_code = 'en'
        );
        
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        SELECT key_id, 'ua',
        CASE r.key_name
            WHEN 'tools.scriptGenerator.howItWorks.step1' THEN 'Заповніть деталі кампанії, цільову URL-адресу та бюджет.'
            WHEN 'tools.scriptGenerator.howItWorks.step2' THEN 'Створіть привабливі оголошення, додавши кілька заголовків і описів.'
            WHEN 'tools.scriptGenerator.howItWorks.step3' THEN 'Перерахуйте відповідні ключові слова, які запускатимуть ваші оголошення.'
            WHEN 'tools.scriptGenerator.howItWorks.step4' THEN 'За бажанням увімкніть сповіщення Telegram для оновлень виконання скрипта.'
            WHEN 'tools.scriptGenerator.howItWorks.step5' THEN 'Натисніть "Згенерувати скрипт", щоб отримати готовий до використання Google Ads скрипт.'
            WHEN 'tools.scriptGenerator.sections.campaignDetails' THEN 'Деталі кампанії та групи оголошень'
            WHEN 'tools.scriptGenerator.sections.adContent' THEN 'Зміст оголошення'
            WHEN 'tools.scriptGenerator.sections.keywords' THEN 'Ключові слова'
            WHEN 'tools.scriptGenerator.sections.telegram' THEN 'Сповіщення Telegram (необов''язково)'
            WHEN 'tools.scriptGenerator.fields.finalUrl' THEN 'Фінальна URL'
            WHEN 'tools.scriptGenerator.fields.campaignName' THEN 'Назва кампанії'
            WHEN 'tools.scriptGenerator.fields.adGroupName' THEN 'Назва групи оголошень'
            WHEN 'tools.scriptGenerator.fields.dailyBudget' THEN 'Щоденний бюджет'
            WHEN 'tools.scriptGenerator.fields.headlines' THEN 'Заголовки (до 15)'
            WHEN 'tools.scriptGenerator.fields.descriptions' THEN 'Описи (до 4)'
            WHEN 'tools.scriptGenerator.fields.keyword' THEN 'Ключове слово'
            WHEN 'tools.scriptGenerator.fields.enableTelegram' THEN 'Увімкнути сповіщення Telegram'
            WHEN 'tools.scriptGenerator.fields.telegramBotToken' THEN 'Токен Telegram бота'
            WHEN 'tools.scriptGenerator.fields.telegramChatId' THEN 'ID чату Telegram'
            WHEN 'tools.scriptGenerator.placeholders.headline' THEN 'Заголовок'
            WHEN 'tools.scriptGenerator.placeholders.description' THEN 'Опис'
            WHEN 'tools.scriptGenerator.placeholders.keyword' THEN 'наприклад, купити онлайн віджети'
            WHEN 'tools.scriptGenerator.tooltips.finalUrl' THEN 'URL-адреса цільової сторінки для ваших оголошень.'
            WHEN 'tools.scriptGenerator.tooltips.campaignName' THEN 'Назва для вашої нової кампанії.'
            WHEN 'tools.scriptGenerator.tooltips.adGroupName' THEN 'Назва для основної групи оголошень у цій кампанії.'
            WHEN 'tools.scriptGenerator.tooltips.dailyBudget' THEN 'Щоденний бюджет для кампанії (наприклад, 50). Встановіть 0 для відсутності бюджету (використовує спільний бюджет).'
            WHEN 'tools.scriptGenerator.tooltips.removeHeadline' THEN 'Видалити заголовок'
            WHEN 'tools.scriptGenerator.tooltips.removeDescription' THEN 'Видалити опис'
            WHEN 'tools.scriptGenerator.tooltips.removeKeyword' THEN 'Видалити ключове слово'
            WHEN 'tools.scriptGenerator.tooltips.telegramBotToken' THEN 'Ваш API токен Telegram бота.'
            WHEN 'tools.scriptGenerator.tooltips.telegramChatId' THEN 'Ваш ID користувача/групи чату Telegram.'
            WHEN 'tools.scriptGenerator.buttons.addHeadline' THEN 'Додати заголовок'
            WHEN 'tools.scriptGenerator.buttons.addDescription' THEN 'Додати опис'
            WHEN 'tools.scriptGenerator.buttons.addKeyword' THEN 'Додати нове ключове слово'
            WHEN 'tools.scriptGenerator.buttons.generateScript' THEN 'Згенерувати скрипт'
            WHEN 'tools.scriptGenerator.results.title' THEN 'Згенерований Google Ads скрипт'
            WHEN 'tools.scriptGenerator.errors.requiredFields' THEN 'Назва кампанії, назва групи оголошень та фінальна URL є обов''язковими.'
            WHEN 'tools.scriptGenerator.errors.invalidBudget' THEN 'Бюджет має бути позитивним числом.'
            WHEN 'tools.scriptGenerator.errors.invalidUrl' THEN 'Будь ласка, введіть дійсну фінальну URL (наприклад, https://example.com).'
            WHEN 'tools.scriptGenerator.errors.telegramRequired' THEN 'Токен Telegram бота та ID чату є обов''язковими, коли увімкнені сповіщення Telegram.'
            WHEN 'tools.scriptGenerator.errors.noAds' THEN 'Будь ласка, додайте принаймні одне оголошення із заголовками та описами.'
            WHEN 'tools.scriptGenerator.errors.incompleteAds' THEN 'Будь ласка, переконайтеся, що всі активні оголошення мають принаймні один заголовок та один опис.'
            WHEN 'tools.scriptGenerator.errors.noKeywords' THEN 'Будь ласка, додайте принаймні одне ключове слово.'
            WHEN 'tools.scriptGenerator.success.generated' THEN 'Скрипт успішно згенерований!'
            WHEN 'common.backToForm' THEN 'Повернутися до форми'
            WHEN 'common.generateScript' THEN 'Згенерувати скрипт'
            WHEN 'common.enableTelegram' THEN 'Увімкнути сповіщення Telegram'
            WHEN 'common.generating' THEN 'Генерація...'
            WHEN 'common.scriptGenerated' THEN 'Скрипт успішно згенерований!'
            WHEN 'dashboard.openTool' THEN 'Відкрити інструмент'
            WHEN 'dashboard.toolsSection' THEN 'Інструменти'
            WHEN 'dashboard.recentAdjustments' THEN 'Останні коригування бюджету'
            WHEN 'auth.role' THEN 'Роль'
            WHEN 'auth.status' THEN 'Статус'
            WHEN 'auth.active' THEN 'Активний'
            ELSE 'Потребує перекладу'
        END
        WHERE NOT EXISTS (
            SELECT 1 FROM content_translations ct 
            WHERE ct.content_key_id = key_id AND ct.language_code = 'ua'
        );
    END LOOP;
END $$;