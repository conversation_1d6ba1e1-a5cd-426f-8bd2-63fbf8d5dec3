version: '3.8'

# Production overrides for docker-compose.yml
services:
  postgres:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-change-in-production}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  backend:
    environment:
      NODE_ENV: production
      DB_PASSWORD: ${POSTGRES_PASSWORD:-change-in-production}
      JWT_SECRET: ${JWT_SECRET:-change-this-super-secret-key-in-production}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://yourdomain.com}
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  frontend:
    environment:
      VITE_API_URL: ${VITE_API_URL:-https://api.yourdomain.com}
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    volumes:
      - redis_prod_data:/data

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: gads-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - gads-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

volumes:
  postgres_prod_data:
  redis_prod_data:
