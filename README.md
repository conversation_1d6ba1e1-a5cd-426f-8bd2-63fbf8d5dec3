# gAds Supercharge

A comprehensive Google Ads automation and management platform built with React, TypeScript, and Vite.

## 🚀 Quick Start

### Frontend Only (Current)
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Full-Stack with Docker
```bash
# Clone and setup
git clone <repository-url>
cd gads-supercharge

# Start all services (PostgreSQL + Backend + Frontend)
./start.sh

# Or manually
docker-compose up -d
```

### Access Points
- **Frontend**: <http://localhost:5173>
- **Backend API**: <http://localhost:3001>
- **Database**: localhost:5432

## ✨ Full-Stack Features

### Core Functionality
- **Script Generators**: Automated Google Ads script generation with obfuscated variables
- **Telegram Integration**: Performance reports to Telegram channels
- **Airtable Integration**: P&L reporting automation
- **Budget Management**: Automated budget updates with notifications
- **Performance Analytics**: Campaign and keyword performance tracking

### Advanced Features
- **User Authentication**: JWT-based secure login with session management
- **Multilingual Support**: Database-driven English/Ukrainian translations
- **Activity Tracking**: Comprehensive user activity logging and analytics
- **Real-time Language Switching**: Seamless language changes without page reload
- **Role-based Access**: Admin and user roles with different permissions
- **Session Management**: Device tracking and session analytics

### Security & Performance
- **Variable Obfuscation**: All generated scripts use randomized variable names
- **Rate Limiting**: DDoS protection and API throttling
- **CORS Protection**: Secure cross-origin resource sharing
- **Health Monitoring**: Service health checks and monitoring
- **Docker Containerization**: Production-ready deployment

## 🔐 Login

- **URL**: http://localhost:5173/login
- **Email**: `<EMAIL>`
- **Password**: `hIgio12@pf`

## 📚 Documentation

- **[Full Documentation](docs/README.md)** - Complete project documentation
- **[Project Context](docs/context.md)** - Architecture and components
- **[Deployment Guide](docs/deployment-context.md)** - Deployment instructions
- **[Optimization Plan](docs/netlify-optimization-plan.md)** - Performance optimization

## 🛠️ Available Tools

- Google Ads Budget Updater
- Telegram Script Generator
- AirTable Script Generator
- Campaign Performance Analysis
- Keyword Performance Tracking
- Device Bid Optimization
- And many more...

## 🌐 Live Demo

**Production**: https://gads-supercharge.netlify.app/ (Legacy frontend-only)
**Development**: http://localhost:5175 (Full-stack with database)

## 🆕 Recent Updates (2025-01-13)

### ✅ Bilingual Support
- **209+ translation keys** in Ukrainian and English
- **Real-time language switching** without page reload
- **Database-driven content management** for easy updates
- **SEO optimization** with language-specific meta tags

### ✅ Pages Fully Translated
- Homepage, About, Services, Contact, Careers
- Dashboard with all tool descriptions
- Legal pages (Privacy, Terms, Cookies)

### ✅ Technical Improvements
- Fixed footer duplication issues
- Enhanced routing with proper SEO wrappers
- Optimized database content loading
- Improved user experience and navigation

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.
