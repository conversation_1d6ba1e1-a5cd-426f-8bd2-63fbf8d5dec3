version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: gads-postgres
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: gads_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gads-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gads_user -d gads_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: gads-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: gads_password
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      CORS_ORIGIN: http://localhost:5173
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - gads-network
    restart: unless-stopped
    volumes:
      - ./backend:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: gads-frontend
    environment:
      VITE_API_URL: http://localhost:3001/api
      VITE_NODE_ENV: production
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - gads-network
    restart: unless-stopped
    volumes:
      - .:/app
      - /app/node_modules
      - /app/backend

  # Redis for Session Management (Optional)
  redis:
    image: redis:7-alpine
    container_name: gads-redis
    ports:
      - "6379:6379"
    networks:
      - gads-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:

networks:
  gads-network:
    driver: bridge
