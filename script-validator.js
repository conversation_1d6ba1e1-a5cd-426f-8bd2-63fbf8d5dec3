// Script Validator for Google Ads Scripts
// This script validates the syntax and structure of generated scripts

const fs = require('fs');

function validateJavaScriptSyntax(scriptContent) {
  try {
    // Try to parse the script as JavaScript
    new Function(scriptContent);
    return { valid: true, error: null };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

function analyzeVariableObfuscation(scriptContent) {
  const lines = scriptContent.split('\n');
  const variables = [];
  const suspiciousPatterns = [
    /var\s+\w*[Tt]oken\w*/g,
    /var\s+\w*[Cc]hat[Ii]d\w*/g,
    /var\s+\w*[Aa]pi[Kk]ey\w*/g,
    /var\s+\w*[Uu]rl\w*/g,
    /var\s+\w*[Cc]ost\w*/g,
    /var\s+\w*[Cc]licks\w*/g,
    /var\s+\w*[Ii]mpressions\w*/g
  ];

  // Extract all variable declarations
  lines.forEach((line, index) => {
    const varMatch = line.match(/var\s+(\w+)/g);
    if (varMatch) {
      varMatch.forEach(match => {
        const varName = match.replace('var ', '').trim();
        variables.push({ name: varName, line: index + 1 });
      });
    }
  });

  // Check for suspicious patterns
  const suspiciousVars = [];
  suspiciousPatterns.forEach(pattern => {
    const matches = scriptContent.match(pattern);
    if (matches) {
      suspiciousVars.push(...matches);
    }
  });

  return {
    totalVariables: variables.length,
    variables: variables,
    suspiciousVariables: suspiciousVars,
    isObfuscated: suspiciousVars.length === 0
  };
}

function validateGoogleAdsScriptStructure(scriptContent) {
  const requiredElements = {
    mainFunction: /function\s+\w+\s*\(\s*\)\s*{/,
    adsAppUsage: /AdsApp\./,
    tryBlock: /try\s*{/,
    catchBlock: /catch\s*\(/,
    loggerUsage: /Logger\.log/
  };

  const results = {};
  Object.keys(requiredElements).forEach(element => {
    results[element] = requiredElements[element].test(scriptContent);
  });

  return results;
}

function generateTestReport(scriptContent, scriptType) {
  console.log(`\n=== ${scriptType} Script Validation Report ===`);
  
  // 1. Syntax Validation
  const syntaxResult = validateJavaScriptSyntax(scriptContent);
  console.log(`\n1. JavaScript Syntax: ${syntaxResult.valid ? '✅ VALID' : '❌ INVALID'}`);
  if (!syntaxResult.valid) {
    console.log(`   Error: ${syntaxResult.error}`);
  }

  // 2. Variable Obfuscation Analysis
  const obfuscationResult = analyzeVariableObfuscation(scriptContent);
  console.log(`\n2. Variable Obfuscation: ${obfuscationResult.isObfuscated ? '✅ OBFUSCATED' : '❌ NOT OBFUSCATED'}`);
  console.log(`   Total Variables: ${obfuscationResult.totalVariables}`);
  if (obfuscationResult.suspiciousVariables.length > 0) {
    console.log(`   ⚠️  Suspicious Variables Found:`);
    obfuscationResult.suspiciousVariables.forEach(varName => {
      console.log(`      - ${varName}`);
    });
  }

  // 3. Google Ads Script Structure
  const structureResult = validateGoogleAdsScriptStructure(scriptContent);
  console.log(`\n3. Google Ads Script Structure:`);
  Object.keys(structureResult).forEach(element => {
    const status = structureResult[element] ? '✅' : '❌';
    console.log(`   ${element}: ${status}`);
  });

  // 4. Security Analysis
  console.log(`\n4. Security Analysis:`);
  const hasHardcodedSecrets = /['"][a-zA-Z0-9]{20,}['"]/.test(scriptContent);
  console.log(`   Hardcoded Secrets: ${hasHardcodedSecrets ? '⚠️  FOUND' : '✅ NONE'}`);
  
  const hasObviousApiKeys = /api[_-]?key|token|secret/i.test(scriptContent);
  console.log(`   Obvious API References: ${hasObviousApiKeys ? '⚠️  FOUND' : '✅ NONE'}`);

  return {
    syntax: syntaxResult,
    obfuscation: obfuscationResult,
    structure: structureResult,
    overall: syntaxResult.valid && obfuscationResult.isObfuscated
  };
}

// Example usage:
if (require.main === module) {
  console.log('Script Validator Ready');
  console.log('Usage: node script-validator.js <script-file>');
  
  if (process.argv[2]) {
    const scriptFile = process.argv[2];
    try {
      const scriptContent = fs.readFileSync(scriptFile, 'utf8');
      generateTestReport(scriptContent, 'Test');
    } catch (error) {
      console.error('Error reading file:', error.message);
    }
  }
}

module.exports = {
  validateJavaScriptSyntax,
  analyzeVariableObfuscation,
  validateGoogleAdsScriptStructure,
  generateTestReport
};
