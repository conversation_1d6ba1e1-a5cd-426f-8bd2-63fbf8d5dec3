import { writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const pages = [
  { url: '/', lastmod: new Date().toISOString().split('T')[0], changefreq: 'daily', priority: '1.0' },
  { url: '/services', lastmod: new Date().toISOString().split('T')[0], changefreq: 'weekly', priority: '0.9' },
  { url: '/features', lastmod: new Date().toISOString().split('T')[0], changefreq: 'weekly', priority: '0.8' },
  { url: '/pricing', lastmod: new Date().toISOString().split('T')[0], changefreq: 'monthly', priority: '0.7' },
  { url: '/contact', lastmod: new Date().toISOString().split('T')[0], changefreq: 'monthly', priority: '0.8' },
  { url: '/portfolio', lastmod: new Date().toISOString().split('T')[0], changefreq: 'weekly', priority: '0.9' },
  { url: '/about', lastmod: new Date().toISOString().split('T')[0], changefreq: 'monthly', priority: '0.7' },
  { url: '/careers', lastmod: new Date().toISOString().split('T')[0], changefreq: 'monthly', priority: '0.6' },
  { url: '/privacy-policy', lastmod: new Date().toISOString().split('T')[0], changefreq: 'yearly', priority: '0.3' },
  { url: '/terms-of-service', lastmod: new Date().toISOString().split('T')[0], changefreq: 'yearly', priority: '0.3' },
  { url: '/cookies-policy', lastmod: new Date().toISOString().split('T')[0], changefreq: 'yearly', priority: '0.3' },
];

const generateSitemap = () => {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${pages
  .map(
    (page) => `  <url>
    <loc>https://gads.example.com${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  )
  .join('\n')}
</urlset>`;

  writeFileSync(join(__dirname, '../public/sitemap.xml'), sitemap);
  console.log('✅ Sitemap generated successfully!');
};

generateSitemap();
