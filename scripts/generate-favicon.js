import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { createCanvas } from 'canvas';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create public directory if it doesn't exist
const publicDir = join(__dirname, '../public');
if (!existsSync(publicDir)) {
  mkdirSync(publicDir, { recursive: true });
}

// Create a simple favicon with the gAds logo
function generateFavicon() {
  const sizes = [16, 32, 48, 64, 96, 128, 192, 256, 384, 512];
  
  sizes.forEach(size => {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#1e40af';
    ctx.fillRect(0, 0, size, size);
    
    // Draw a simple "g" logo
    ctx.fillStyle = '#ffffff';
    const center = size / 2;
    const radius = size * 0.4;
    
    // Draw circle
    ctx.beginPath();
    ctx.arc(center, center, radius, 0, Math.PI * 2);
    ctx.fill();
    
    // Draw "g"
    ctx.fillStyle = '#1e40af';
    ctx.font = `bold ${size * 0.6}px Inter`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('g', center, center + (size * 0.05));
    
    // Save as PNG
    const buffer = canvas.toBuffer('image/png');
    writeFileSync(join(publicDir, `favicon-${size}x${size}.png`), buffer);
  });
  
  // Create site.webmanifest
  const manifest = {
    name: 'gAds Services',
    short_name: 'gAds',
    description: 'Professional Google Ads & Marketing Services',
    start_url: '/',
    display: 'standalone',
    background_color: '#1e40af',
    theme_color: '#1e40af',
    icons: [
      {
        src: '/favicon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'any maskable'
      },
      {
        src: '/favicon-512x512.png',
        sizes: '512x512',
        type: 'image/png'
      }
    ]
  };
  
  writeFileSync(
    join(publicDir, 'site.webmanifest'),
    JSON.stringify(manifest, null, 2)
  );
  
  // Create browserconfig.xml for Windows tiles
  const browserConfig = `<?xml version="1.0" encoding="utf-8"?>
<browserconfig>
  <msapplication>
    <tile>
      <square150x150logo src="/mstile-150x150.png"/>
      <TileColor>#1e40af</TileColor>
    </tile>
  </msapplication>
</browserconfig>`;
  
  writeFileSync(join(publicDir, 'browserconfig.xml'), browserConfig);
  
  console.log('✅ Favicon files generated successfully!');
}

generateFavicon();
