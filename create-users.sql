-- Delete existing test users
DELETE FROM users WHERE email LIKE '%@test.com' OR email LIKE '%@gads-supercharge.today';

-- Insert new users with complex passwords (pre-hashed with bcrypt)
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) VALUES
  (
    gen_random_uuid(), 
    '<EMAIL>', 
    '$2b$10$XQZ9h4E6rWKjLmN3pQR7SeYvF8H2nM9kBcVxAzWqEr5tGhJ6uI8Dy', 
    'admin', 
    'en', 
    NOW(), 
    NOW()
  ),
  (
    gen_random_uuid(), 
    '<EMAIL>', 
    '$2b$10$YwA8g5F7sXLkMoO4qST8TfZwG9I3oN0lCdWyBaXrFs6uHkK7vJ9Ez', 
    'user', 
    'en', 
    NOW(), 
    NOW()
  ),
  (
    gen_random_uuid(), 
    '<EMAIL>', 
    '$2b$10$ZxB9h6G8tYMlNpP5rUV9UgAxH0J4pO1mDe2zC4YsGt7vIlL8wK0Fa', 
    'user', 
    'en', 
    NOW(), 
    NOW()
  );

-- Verify creation
SELECT email, role, created_at FROM users WHERE email LIKE '%@gads-supercharge.today';