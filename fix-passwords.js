const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'gads_db',
  password: 'G4d5Str0ng',
  port: 5432,
});

async function updatePasswords() {
  try {
    const adminHash = await bcrypt.hash('admin123', 10);
    const userHash = await bcrypt.hash('user123', 10);
    const demoHash = await bcrypt.hash('demo123', 10);
    
    await pool.query('UPDATE users SET password_hash = $1 WHERE email = $2', [adminHash, '<EMAIL>']);
    await pool.query('UPDATE users SET password_hash = $1 WHERE email = $2', [userHash, '<EMAIL>']);
    await pool.query('UPDATE users SET password_hash = $1 WHERE email = $2', [demoHash, '<EMAIL>']);
    
    console.log('✅ Passwords updated successfully!');
    console.log('<EMAIL> / admin123');
    console.log('<EMAIL> / user123');
    console.log('<EMAIL> / demo123');
    
    await pool.end();
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

updatePasswords();